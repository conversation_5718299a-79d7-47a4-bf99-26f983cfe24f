/*
 * @Author: z<PERSON>bangtong
 * @Date: 2022/7/27
 * @Description:logText主要逻辑实现
 * 注意：writeLogTask线程中不要用logE等函数，如果队列已经满，会阻塞住writeLogTask线程，导致无法处理日志。logText模块内部使用logShow函数打印日志，底层调用printf打印。
 */
#include <cstring>
#include <sys/stat.h>
#include "logTextImpl.h"
#include "fstream"
#include "cstdio"
#include "time.h"
#include "pthread.h"
#include <sys/syscall.h>
#include "string"
#include "showAllVersion.h"
#include "showAllVersionApi.h"
#include <signal.h>
#include <sys/time.h>
#include <algorithm>
#ifdef SUPPORT_BACKTRACE
#include <execinfo.h>
#endif
#include <dirent.h>
#include "compress.h"
#include <vector>

#ifdef USE_ZLIB
#include "zlib.h"
#endif

// todo
// 判断是否初始化 可以从加锁解锁 改为信号量发送
// 增加计数 查看模块是否出现过队列满 flushLogWithSig
// 初始化参数 只保留必要的初始化字段
// 可以动态改变指定日志内存文件大小

// 打印当前进程名
#define SHOW_PROCESS_NAME()                                                         \
    do                                                                              \
    {                                                                               \
        if (SEND_BUF_LEN - sendLen > 0)                                             \
        {                                                                           \
            sendLen += snprintf(sendBuf + sendLen,                                  \
                                SEND_BUF_LEN - sendLen,                             \
                                "------------------%s------------------\n",         \
                                C_LogTextImpl::getObjPtr()->m_processName.c_str()); \
        }                                                                           \
    } while (0)

// 每轮次最多处理100条日志
#define MAX_DEAL_LOG_COUNT_PER_TIME (100)

using namespace std;

// 获取下一个日志文件序列化 编号范围[000-999]
#define NEXT_SERIAL_NUM(a) ((a + 1) % 1000)

// 获取数据锁 在退出函数时自动解锁
#define GUARD_DATA_LOCK std::lock_guard<std::recursive_mutex> guard(m_dataMtx);


C_LogTextImpl g_logTextImpl;

C_LogTextImpl::C_LogTextImpl()
{
    pthread_mutex_init(&m_logQueueBackMtx, 0);
}

C_LogTextImpl::~C_LogTextImpl()
{
    pthread_mutex_destroy(&m_logQueueBackMtx);
}

// C_LogTextImpl& C_LogTextImpl::getObj()
// {
//     return g_logTextImpl;
// }

C_LogTextImpl* C_LogTextImpl::getObjPtr()
{
    return &g_logTextImpl;
}

/**
 * 初始化日志模块
 * @param initParam 初始化参数
 * @return 0成功/错误码
 */
int C_LogTextImpl::init(const LOG_TEXT_INIT_PARAM& initParam)
{
    LOG_TEXT_INIT_PARAM_V2 initParamV2 = {0};

    initParamV2.initParam = initParam;

    return init(initParamV2);
}

/**
 * 初始化日志模块。内部注册了SIGINT SIGTERM SIGSEGV这3个信号的处理
 * 如果其他模块也有注册信号处理，上层业务可以参考下面链接解决冲突
 * https://stackoverflow.com/questions/17102919/is-it-valid-to-have-multiple-signal-handlers-for-same-signal
 * @param initParam 初始化参数
 * @return 0成功/错误码
 */
int C_LogTextImpl::init(const LOG_TEXT_INIT_PARAM_V2& initParam)
{
    std::string        statusFilePath;
    std::ifstream      statusInput;
    std::ostringstream tmp;
    std::string        statusStr;
    std::string        line;
    std::string        tmpLogFilepath;
    int                ret       = 0;
    int                returnRet = 0;

    GUARD_DATA_LOCK

    if (m_inited)
    {
        returnRet = LOG_TEXT_OK;
        goto exit;
    }

    initClassParam();

    ret = checkInitParam(initParam);
    if (ret != LOG_TEXT_OK)
    {
        returnRet = ret;
        goto exit;
    }

    // 写入导出日志的脚本
    writeExportLogScript();

    regLogTextShellCmd();

    if (NULL == m_pLogQueue)
    {
        m_pLogQueue = (char*)malloc(m_logQueueSize);
        if (NULL == m_pLogQueue)
        {
            logShow("malloc mem for log queue err\n");
            returnRet = LOG_TEXT_MEM_ERR;
            goto exit;
        }
        memset_s(m_pLogQueue, m_logQueueSize, 0);
    }

    // 第二个参数表示该信号量是否可以在多个进程间共享，0表示不共享
    // 第三个参数为信号量初始值
    ret = sem_init(&m_semLog, 0, 0);
    if (ret != 0)
    {
        logShow("sem_init error");
        returnRet = LOG_TEXT_SEM_ERR;
        goto exit;
    }

    // 存档意外关机未保存的日志文件
    ret = atchivistTmpLog();
    if (ret != LOG_TEXT_OK)
    {
        returnRet = ret;
        goto exit;
    }

    // 开启线程 异步写日志
    ret = pthread_create(&m_writeLogThreadFd, NULL, &writeLogTask, NULL);
    if (ret != 0)
    {
        logShow("start writeLogTask failed\n");
        returnRet = LOG_TEXT_START_THREAD_ERR;
        goto exit;
    }

    // 初始化showAllVersion命令
    C_ShowAllVersion::getObj().init();

    // 注册版本信息
    REG_VERSION_INFO("logText", LOG_TEXT_VERSION, "debug log, support compress log file");

    // 初始化需要捕捉的信号列表
    m_sigVector.clear();
    m_sigVector.push_back(SIGINT);    // ctrl+c退出前台程序信号
    m_sigVector.push_back(SIGTERM);   // kill命令默认信号
    m_sigVector.push_back(SIGSEGV);   // 内存访问异常，程序崩溃信号
    m_sigVector.push_back(SIGABRT);   // 可能的情况 1.内存重复free 2.执行abort函数  3.执行assert函数

    m_inited = true;

    // 注册信号捕捉处理，因为有日志缓存在内存中，如果程序退出，将丢失内存中的日志
    // 特别是在程序崩溃的场景，崩溃前的一段时间内的日志将丢失，问题排查将很困难
    // 所以注册信号捕捉，在程序退出前将日志刷入磁盘
    // 目前对于异常断电场景怎么保存内存中的日志还没什么好方法，以后优化吧
    if (m_bCatchSignal)
    {
        // 如果不捕获 不要调用setCatchSignal(false) 因为其他模块如果注册了捕获，会影响其他模块
        setCatchSignal(true);
    }

    // 初始化信号安全检查
    m_signalSafeCheck.init();

    returnRet = LOG_TEXT_OK;
    logShow("logText init ok");

exit:
    // 只有初始化失败的时候才释放资源
    if (!m_inited)
    {
        TRY_FREE_MEM(m_pLogQueue, free)
    }

    return returnRet;
}

/**
  * 反初始化，停止记录日志，释放资源
  */
void C_LogTextImpl::unInit()
{
    int ret = 0;
    {
        GUARD_DATA_LOCK

        if (!m_inited)
        {
            logShow("C_LogTextImpl has been uninited, can not uninit again");
            return;
        }

        // 反初始化顺序要和初始化顺序相反，先停止线程，再释放线程用到的资源
        m_inited = false;
        sem_post(&m_semLog);

        // 将对应信号的处理函数设置为系统默认处理函数
        if (m_bCatchSignal)
        {
            setCatchSignal(false);
        }
        m_sigVector.clear();
    }

    if ((int)m_writeLogThreadFd != -1)
    {
        pthread_join(m_writeLogThreadFd, NULL);
    }
    m_writeLogThreadFd = -1;

    ret = sem_destroy(&m_semLog);
    if (ret != 0)
    {
        logShow("sem_destroy failed");
    }
    TRY_FREE_MEM(m_pLogQueue, free)
}

/**
 * 设置输出的格式，是否要带文件名、行号等字段
 * @param format 格式
 * @return 0成功/错误码
 */
int C_LogTextImpl::setFormat(unsigned long long format)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setFormat need init first");
        return LOG_TEXT_NOT_INIT;
    }

    m_logFormat = format;
    return LOG_TEXT_OK;
}

unsigned long long C_LogTextImpl::getFormat()
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("getFormat need init first");
        return LOG_TEXT_NOT_INIT;
    }
    unsigned long long res;
    res = m_logFormat;
    return res;
}

/**
 * 输出到终端的日志是否带颜色
 * @param enable true带颜色 false不带颜色
 */
void C_LogTextImpl::setColorOutput(bool enable)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setColorOutput need init first");
        return;
    }
    m_colorOutput = enable;
}

/**
 * 是否输出到标准输出
 * @param enable true输出 false不输出
 */
void C_LogTextImpl::setStdOutput(bool enable)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setStdOutput need init first");
        return;
    }
    m_stdOutput = enable;
}

/**
 * 设置所有日志的输出等级
 * @param logLevel 日志等级
 * @return 0成功/错误码
 */
int C_LogTextImpl::setLogLevel(LOG_LEVEL logLevel)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setLogLevel need init first");
        return LOG_TEXT_NOT_INIT;
    }

    if (logLevel <= LOG_LEVEL_MIN_BORDER || logLevel >= LOG_LEVEL_MAX_BORDER)
    {
        logShow("setLogLevel err, logLevel[%d] err, must between [%d,%d]\n",
                logLevel,
                LOG_LEVEL_MIN_BORDER + 1,
                LOG_LEVEL_MAX_BORDER - 1);
        return LOG_TEXT_PARAM_ERR;
    }
    m_globalLogLevel = logLevel;   // 记录全局打印等级

    // 当没有指定模块名时，默认调整所有模块对应的打印等级，所以直接清空m_moduleLogLevelMap即可
    // m_moduleLogLevelMap是模块名到打印等级的映射，用于记录模块名对应的打印等级
    m_moduleLogLevelMap.clear();
    return LOG_TEXT_OK;
}

/**
 * 设置某个模块的日志输出等级
 * @param logLevel 日志等级
 * @param module 模块名
 * @return 0成功/错误码
 */
int C_LogTextImpl::setLogLevel(LOG_LEVEL logLevel, const char* module)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setLogLevel need init first");
        return LOG_TEXT_NOT_INIT;
    }

    if (module == NULL)
    {
        logShow("module is null\n");
        return LOG_TEXT_PARAM_ERR;
    }

    if (OSI_Strnlen_Safe(module, 1024) == 0)
    {
        logShow("module len is 0\n");
        return LOG_TEXT_PARAM_ERR;
    }

    if (logLevel <= LOG_LEVEL_MIN_BORDER || logLevel >= LOG_LEVEL_MAX_BORDER)
    {
        logShow("setLogLevel err, logLevel[%d] err, must between [%d,%d]\n",
                logLevel,
                LOG_LEVEL_MIN_BORDER + 1,
                LOG_LEVEL_MAX_BORDER - 1);
        return LOG_TEXT_PARAM_ERR;
    }

    m_moduleLogLevelMap[module] = logLevel;
    return LOG_TEXT_OK;
}

/**
 * @brief 设置日志是否实时同步到磁盘
 *
 * @param enable true实时写入磁盘，一般测试的时候才会用到  false仅在内存中缓存
 * @return int 0成功/错误码
 */
int C_LogTextImpl::setSyncDisk(bool enable)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setSyncDisk need init first");
        return LOG_TEXT_NOT_INIT;
    }

    int    ret = 0;
    string cmd;
    string res;

    m_syncDisk = enable;
    if (enable)
    {
        if (m_memLogSize > 0)
        {
            // 拷贝文件
            cmd = "cp " + m_memLogDir + TMP_LOG_FILENAME + " " + m_logDir + TMP_LOG_FILENAME;
            ret = C_LogUtils::runCmd(cmd, res);
            if (ret != 0)
            {
                logShow("sync log to file err, ret[%d]", ret);
            }
        }
    }
    else
    {
        ret = remove((m_logDir + TMP_LOG_FILENAME).c_str());
        if (ret != 0)
        {
            logShow("remove tmp log file failed, but do not worry");
        }
    }

    return 0;
}

void C_LogTextImpl::flushLog()
{
    FLUSH_INFO info = {LOG_FLUSH_TYPE_NOT_FLUSH, 0};

    info.flushType = LOG_FLUSH_TYPE_API;

    flushLogWithInfo(info);
}

void C_LogTextImpl::flushLogWithInfo(const FLUSH_INFO& info)
{
    LOG_FLUSH_TYPE flag = LOG_FLUSH_TYPE_NOT_FLUSH;

    if (!m_inited)
    {
        logShow("flush failed, because logText not inited");
        return;
    }
    {
        GUARD_DATA_LOCK
        m_flushInfo = info;
    }

    sem_post(&m_semLog);

    while (true)
    {
        {
            GUARD_DATA_LOCK
            flag = m_flushInfo.flushType;
        }
        if (flag == LOG_FLUSH_TYPE_NOT_FLUSH)
        {
            break;
        }
        usleep(100 * 1000);
    }

    if (m_flushLogCb)
    {
        vector<LOG_FILE_INFO> logFileInfos;
        vector<string>        logFilepaths;
        if (OSI_OK != getLogFileInfo(m_logDir, logFileInfos))
        {
            logShow("get log file info failed");
            return;
        }
        for (auto& logFileInfo : logFileInfos)
        {
            logFilepaths.push_back(logFileInfo.filepath);
        }
        m_flushLogCb(logFilepaths);
    }
}

/**
 * @brief 设置是否要捕获信号
 *
 * @param enable true捕获 false不捕获
 * @return 0成功/错误码
 */
int C_LogTextImpl::setCatchSignal(bool enable)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setCatchSignal need init first");
        return LOG_TEXT_NOT_INIT;
    }

    if (enable)
    {
        for (size_t i = 0; i < m_sigVector.size(); i++)
        {
            signal(m_sigVector[i], signalHandler);
        }
    }
    else
    {
        for (size_t i = 0; i < m_sigVector.size(); i++)
        {
            signal(m_sigVector[i], SIG_DFL);
        }
    }
    m_bCatchSignal = enable;
    return OSI_OK;
}

/**
 * @brief 注册信号处理函数
 *
 * @param f 处理函数
 * @return 0成功/错误码
 */
int C_LogTextImpl::regSigHandler(SigHandlerFun f)
{
    GUARD_DATA_LOCK
    m_sigHandlerList.push_back(f);
    return 0;
}

/**
 * @brief 获取当前将捕获的信号值列表
 * 该函数需要需要在init之后调用才能生效
 * @param signums 返回当前会捕获的信号值列表
 * @return 0成功/错误码
 */
int C_LogTextImpl::getSignums(std::vector<int>& signums)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("getSignums failed, logText not inited");
        return LOG_TEXT_NOT_INIT;
    }

    signums = m_sigVector;

    return OSI_OK;
}

/**
 * @brief 设置需要捕获的信号值，一般捕获的都是程序异常退出相关的信号值，比如SIGSEGV，logText捕获到信号值之后会进行一些信息记录和收尾工作
 * 该函数需要需要在init之后调用才能生效
 * @param signums 需要捕获的信号值列表
 * @return 0成功/错误码
 */
int C_LogTextImpl::setSignums(const std::vector<int>& signums)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setSignums need init first");
        return LOG_TEXT_NOT_INIT;
    }

    if (!m_inited)
    {
        logShow("setSignums failed, logText not inited");
        return LOG_TEXT_NOT_INIT;
    }

    if (m_bCatchSignal)
    {
        // 恢复信号处理函数
        setCatchSignal(false);

        // 注册信号处理函数
        m_sigVector = signums;
        setCatchSignal(true);
    }
    else
    {
        // 如果没有开启捕获信号功能 仅记录需要捕获的信号列表 不注册处理函数
        m_sigVector = signums;
    }

    return OSI_OK;
}


void C_LogTextImpl::osiDebug(LOG_LEVEL   logLevel,
                             const char* module,
                             const char* file,
                             const char* fun,
                             int         line,
                             const char* fmt,
                             int         count,
                             va_list     ap)
{
    logfDo(logLevel, module, file, fun, line, fmt, count, ap);
}

void C_LogTextImpl::logfDo(LOG_LEVEL   logLevel,
                           const char* module,
                           const char* file,
                           const char* fun,
                           int         line,
                           const char* fmt,
                           int         count,
                           va_list     ap)
{
    int         level          = 0;
    char*       logWriteBuf    = NULL;
    int         writeLen       = 0;
    char        levelName[][3] = {"T", "I", "R", "W", "E", "F"};
    int         ret            = 0;
    LOG_HEADER  logHeader;
    LOG_HEADER* pLogHeader = NULL;
    bool        dropLog    = true;   // 是否丢弃该条日志
    int         lastBak    = 0;


    if (!m_inited)
    {
        // 如果未初始化 则直接打印
        if (logLevel >= LOG_LEVEL_WARNING)
        {
            logShow("logText not init, so just printf");
            if (count == 0)
            {
                printf("[file:%s:%d][fun:%s]%s\n\n", C_LogUtils::getFilename(file).c_str(), line, fun, fmt);
            }
            else
            {
                char tmp[256];
                memset_s(&tmp, sizeof(tmp), 0);
                ret = vsnprintf(tmp, sizeof(tmp), fmt, ap);
                printf("[file:%s:%d][fun:%s]%s\n\n", C_LogUtils::getFilename(file).c_str(), line, fun, tmp);
            }
        }
        return;
    }

    if (module == NULL || file == NULL || fun == NULL || fmt == NULL)
    {
        logShow("log err, param has null, module[%p] file[%p] fun[%p] fmt[%p]\n", module, file, fun, fmt);
        return;
    }

    memset_s(&logHeader, sizeof(logHeader), 0);
    logHeader.logLevel = logLevel;

    // 判断等级是否需要输出 先判断module有没有单独设置输出等级，如果没有就用全局的等级
    level = m_globalLogLevel;
    if (m_moduleLogLevelMap.find(module) != m_moduleLogLevelMap.end())
    {
        level = m_moduleLogLevelMap[module];
    }
    logHeader.output = logLevel >= level;
    logHeader.record = m_logFileLevel == -1 ? logHeader.output : logLevel >= m_logFileLevel;

    // 如果既不需要打印输出，也不需要记录到文件，直接返回
    if (!logHeader.output && !logHeader.record)
    {
        return;
    }

    // 从缓存队列中获取日志存放地址
    pthread_mutex_lock(&m_logQueueBackMtx);

    // m_logQueueBack指向的位置会一直保持可以写入状态
    // 向队列末尾写入日志  日志格式：日志头+日志信息
    pLogHeader  = (LOG_HEADER*)(m_pLogQueue + m_logQueueBack);
    logWriteBuf = m_pLogQueue + m_logQueueBack;

    // 向内存中写入日志信息
    OSI_Memcpy_Safe(logWriteBuf + writeLen, &logHeader, sizeof(logHeader));
    writeLen += sizeof(logHeader);
    // 根据使能的日志字段，组装日志内容
    if (m_logFormat & LOG_TEXT_FMT_LEVEL)
    {
        if (ONE_LOG_MAX_LEN - writeLen > 0)
        {
            writeLen += snprintf(logWriteBuf + writeLen, ONE_LOG_MAX_LEN - writeLen, "[%s]", levelName[logLevel]);
        }
    }
    if (m_logFormat & LOG_TEXT_FMT_TIME)
    {
        if (ONE_LOG_MAX_LEN - writeLen > 0)
        {
            writeLen += snprintf(
                logWriteBuf + writeLen, ONE_LOG_MAX_LEN - writeLen, "[%s]", C_LogUtils::getLocalTimeWithUs().c_str());
        }
    }
    if (m_logFormat & LOG_TEXT_FMT_PROCESS_NAME)
    {
        if (ONE_LOG_MAX_LEN - writeLen > 0)
        {
            writeLen += snprintf(logWriteBuf + writeLen, ONE_LOG_MAX_LEN - writeLen, "[%s]", m_processName.c_str());
        }
    }
    if (m_logFormat & LOG_TEXT_FMT_TID)
    {
        if (ONE_LOG_MAX_LEN - writeLen > 0)
        {
            writeLen +=
                snprintf(logWriteBuf + writeLen, ONE_LOG_MAX_LEN - writeLen, "[tid:%04ld]", syscall(SYS_gettid));
        }
    }
    if (m_logFormat & LOG_TEXT_FMT_MODULE)
    {
        if (ONE_LOG_MAX_LEN - writeLen > 0)
        {
            writeLen += snprintf(logWriteBuf + writeLen, ONE_LOG_MAX_LEN - writeLen, "[module:%s]", module);
        }
    }
    if (m_logFormat & LOG_TEXT_FMT_FUN)
    {
        if (ONE_LOG_MAX_LEN - writeLen > 0)
        {
            writeLen += snprintf(logWriteBuf + writeLen, ONE_LOG_MAX_LEN - writeLen, "[fun:%s]", fun);
        }
    }
    if (m_logFormat & LOG_TEXT_FMT_FILE)
    {
        if (ONE_LOG_MAX_LEN - writeLen > 0)
        {
            // 文件只记录文件名，不记录绝对路径
            writeLen += snprintf(logWriteBuf + writeLen,
                                 ONE_LOG_MAX_LEN - writeLen,
                                 "[file:%s:%d]",
                                 C_LogUtils::getFilename(file).c_str(),
                                 line);
        }
    }
    if (m_logFormat & LOG_TEXT_FMT_SEPARATOR)
    {
        if (ONE_LOG_MAX_LEN - writeLen > 0)
        {
            writeLen += snprintf(logWriteBuf + writeLen, ONE_LOG_MAX_LEN - writeLen, LOG_SEPARATOR);
        }
    }
    if (ONE_LOG_MAX_LEN - writeLen > 0)
    {
        if (count == 0)
        {
            // 当可变参个数为0时，自动转换打印方式
            // 比如有个字符串tmp="ok 10%send",使用者如果不注意使用printf(tmp),那么%s就会被误解析为格式化参数，导致打印失败甚至程序崩溃
            // 此时自动将格式转换为printf("%s",tmp),可以正常输出
            writeLen += snprintf(logWriteBuf + writeLen, ONE_LOG_MAX_LEN - writeLen, "%s", fmt);
        }
        else
        {
            writeLen += vsnprintf(logWriteBuf + writeLen, ONE_LOG_MAX_LEN - writeLen, fmt, ap);
        }
    }

    // 添加换行符
    if (writeLen < ONE_LOG_MAX_LEN - 2)
    {
        logWriteBuf[writeLen]     = '\n';
        logWriteBuf[writeLen + 1] = '\n';
        logWriteBuf[writeLen + 2] = '\0';
        writeLen += 3;
    }
    else
    {
        logWriteBuf[ONE_LOG_MAX_LEN - 3] = '\n';
        logWriteBuf[ONE_LOG_MAX_LEN - 2] = '\n';
        logWriteBuf[ONE_LOG_MAX_LEN - 1] = '\0';
        writeLen                         = ONE_LOG_MAX_LEN;
    }

    // 更新头部的日志长度信息
    pLogHeader->len = writeLen;
    // 更新队列
    lastBak         = m_logQueueBack;
    m_logQueueBack += writeLen;

    if (m_dropLog)
    {
        // 直到m_logQueueBack找到下一个可写位置 如果在指定时间未完成 则丢弃当前日志记录
        for (int i = 0; i < PUSH_QUEUE_TIMEOUT_MS + 1; i++)
        {
            if (queueBackNext())
            {
                dropLog = false;
                break;
            }
            if (i < PUSH_QUEUE_TIMEOUT_MS)
            {
                usleep(1000);
            }
        }

        // 超过指定时间还不能入队列 直接丢弃日志
        if (dropLog)
        {
            m_dropNum++;
            m_logQueueBack = lastBak;   // 将索引回退，丢弃写入的当前日志
        }
    }
    else
    {
        dropLog = false;
        while (!queueBackNext())
        {
            usleep(1000);
        }
    }

    pthread_mutex_unlock(&m_logQueueBackMtx);

    // 通知写日志线程 有日志可写
    if (!dropLog)
    {
        m_dataMtx.lock();
        m_queueLogCount++;
        m_dataMtx.unlock();
        sem_post(&m_semLog);
    }
}

/**
 * 原始日志输出函数，一般不直接用这个，用封装后的即可。
 * __attribute__((format(printf, 7, 8)))用于检查printf格式化参数(需要编译参数-Wformat或者-Wall)，在类中使用碰到问题看下面连接
 * https://stackoverflow.com/questions/11621043/how-should-i-properly-use-attribute-format-printf-x-y-inside-a-class
 * @param logLevel 日志等级
 * @param module 模块名
 * @param file 文件路径
 * @param fun 函数名
 * @param line 行号
 * @param fmt 格式化输出格式,参考printf
 * @param count 可变参数长度
 * @param ... 格式化输出参数,参考printf
 */
void C_LogTextImpl::logf(LOG_LEVEL   logLevel,
                         const char* module,
                         const char* file,
                         const char* fun,
                         int         line,
                         const char* fmt,
                         int         count,
                         ...)
{
    va_list ap;
    va_start(ap, count);
    logfDo(logLevel, module, file, fun, line, fmt, count, ap);
    va_end(ap);
}

/**
 * 异步写日志，从队列中取出日志，写入内存缓存或者磁盘
 */
void* C_LogTextImpl::writeLogTask(void* args)
{
    C_LogTextImpl*  logTextImplPtr = C_LogTextImpl::getObjPtr();
    int             ret            = 0;
    struct timespec ts             = {0};

    // 等待初始化完毕，m_inited被置为true
    C_LogTextImpl::getObjPtr()->m_dataMtx.lock();
    C_LogTextImpl::getObjPtr()->m_dataMtx.unlock();

    while (true)
    {
        // 一直等到有处理任务 目前有以下三种任务
        // 1. 组件反初始化，需要退出该task
        // 2. 有新的日志打印，需要做处理
        // 3. 需要立即将缓存中的日志写入磁盘
        // 配置超时的目的是可以定时调用checkMemLogDirSize来检测日志内存目录大小触发持久化

        clock_gettime(CLOCK_REALTIME, &ts);
        ts.tv_sec += SEM_WAIT_TIME_S;
        ret = sem_timedwait(&logTextImplPtr->m_semLog, &ts);
        if (ret != 0)
        {
            if (ret == -1 && (errno == ETIMEDOUT || errno == EINTR))
            {
                // 正常超时退出sem_timedwait，不做什么
            }
            else
            {
                // ret == 0 时才是正常收到信号量
                // 如果有其他异常 则打印错误日志 不要break 这样方便通过打印快速定位到问题
                logShow("sem_wait ret[%d] errno[%d] err", ret, errno);
                perror("sem_wait err");
                // break;
            }
        }

        // 检查是否是组件反初始化任务
        if (!logTextImplPtr->m_inited)
        {
            logShow("writeLogTask get off work, goodbye~");
            break;
        }

        // 检测日志内存目录大小，判断是否要持久化日志
        logTextImplPtr->checkMemLogDirSize();

        // 检查是否达到定时持久化日志时间
        logTextImplPtr->checkFlushInterval();

        // 测试模拟线程阻塞时 不执行后面的日志出队列等操作
        // 仅在单元测试时会进入到该逻辑
        if (logTextImplPtr->m_testBlockWriteLogTask != 0)
        {
            if (logTextImplPtr->m_testBlockWriteLogTask > 0)
            {
                // 为正数时，模拟卡顿指定的毫秒数
                usleep(1000 * logTextImplPtr->m_testBlockWriteLogTask);
            }
            else
            {
                printf("start block log queue deal thread\n");
                // 为负数时，直接卡死
                while (true)
                {
                    if (logTextImplPtr->m_testBlockWriteLogTask >= 0)
                    {
                        printf("end block log queue deal thread, queueLogCount[%d] dropNum[%llu] queueBack[%d]\n",
                               logTextImplPtr->m_queueLogCount,
                               logTextImplPtr->m_dropNum,
                               logTextImplPtr->m_logQueueBack);
                        break;
                    }
                    usleep(1000 * 1000);
                }
            }
        }

        // 尝试从队列中取出日志并处理
        dealLogInQueue();

        // 检查是否需要持久化日志
        if (logTextImplPtr->m_flushInfo.flushType != LOG_FLUSH_TYPE_NOT_FLUSH)
        {
            logTextImplPtr->compressAndWriteFile(logTextImplPtr->m_flushInfo);
            logTextImplPtr->m_flushInfo.flushType = LOG_FLUSH_TYPE_NOT_FLUSH;   // 已经完成刷写
        }
    }

    logShow("writeLogTask out");

    return NULL;
}

/**
 * @brief 根据flush类型生成对应的字符串
 */
int C_LogTextImpl::transFlushType2Str(const FLUSH_INFO& info, std::string& str)
{
    char tmp[64] = {0};

    if (info.flushType == LOG_FLUSH_TYPE_SIZE)
    {
        str = "size";
    }
    else if (info.flushType == LOG_FLUSH_TYPE_TIME)
    {
        str = "time";
    }
    else if (info.flushType == LOG_FLUSH_TYPE_API)
    {
        str = "api";
    }
    else if (info.flushType == LOG_FLUSH_TYPE_MANUAL)
    {
        str = "manual";
    }
    else if (info.flushType == LOG_FLUSH_TYPE_SIGNAL)
    {
        OSI_Snprintf(tmp, sizeof(tmp), "signal%d", info.signal);
        str = tmp;
    }
    else if (info.flushType == LOG_FLUSH_TYPE_OLD)
    {
        str = "old";
    }
    else
    {
        str = "unknow";
    }

    return OSI_OK;
}


/**
 * @brief 压缩并生成日志文件
 *
 */
void C_LogTextImpl::compressAndWriteFile(const FLUSH_INFO& info, const std::string orgLogFilepath)
{
    long                  compressResLen = 0;
    vector<LOG_FILE_INFO> logFileInfos;
    vector<LOG_FILE_INFO> logFileInfosBak;
    int                   ret = 0;
    std::string           logFilepath;
    struct timeval        timeBefore;
    struct timeval        timeAfter;
    int                   seconds      = 0;
    int                   microseconds = 0;
    string                logStartTime;
    string                logEndTime;
    string                addName;

    m_lastFlushTime = C_LogUtils::getUptime();

    if (m_memOnly)
    {
        // 如果不需要做持久化相关操作 在某几种场景下要控制日志文件大小
        // logShow("memOnly is true, not flush");

        if (info.flushType == LOG_FLUSH_TYPE_SIZE)
        {
            // 只有日志达到指定大小这一种场景 需要删除日志文件来限制内存占用
            // 这种场景一般是持久化进程已经异常退出，没能持久化日志，导致达到指定大小
            ret = remove((m_memLogDir + TMP_LOG_FILENAME).c_str());
            logShow("remove log file[%s] ret[%d]", (m_memLogDir + TMP_LOG_FILENAME).c_str(), ret);
        }
        goto exit;
    }

    transFlushType2Str(info, addName);

    // 获取当前日志文件信息 获取到的文件列表按生成的先后顺序排序  新->旧
    logFileInfos.clear();
    ret = getLogFileInfo(m_logDir, logFileInfos);
    if (ret != OSI_OK)
    {
        logShow("getLogFileInfo failed, ret[%d]", ret);
        goto exit;
    }

    // 删除老的日志文件 直到符合要求
    ret = clearLogFile(compressResLen, m_logFileSize, logFileInfos);
    if (ret != OSI_OK)
    {
        logShow("getLogFileInfo failed, ret[%d]", ret);
        goto exit;
    }

    // 对日志备份
    if (!m_backupLogDir.empty())
    {
        logFileInfosBak.clear();
        ret = getLogFileInfo(m_backupLogDir, logFileInfosBak);
        if (ret != OSI_OK)
        {
            logShow("getLogFileInfo failed, ret[%d]", ret);
            goto exit;
        }

        ret = clearLogFile(compressResLen, m_backupLogFileSize, logFileInfosBak);
        if (ret != OSI_OK)
        {
            logShow("getLogFileInfo failed, ret[%d]", ret);
            goto exit;
        }
    }

    // 因为当前是打包目录 所以不校验日志文件大小 收到命令就打包持久化
    // if (m_memLogSize == 0)
    // {
    //     logShow("log cache len is 0");
    //     goto exit;
    // }

    gettimeofday(&timeBefore, NULL);

    // 如果没有指定特定日志文件路径，默认打包日志目录
    if (orgLogFilepath.empty())
    {
        // // 生成新的日志文件 并删除内存中的目录
        // ret = scriptFlushLog(addName.c_str());
        // if (ret != OSI_OK)
        // {
        //     logShow("scriptFlushLog failed");
        // }

        ret = zlibFlushLog(logFileInfos, addName.c_str());
        if (ret != OSI_OK)
        {
            logShow("zlibFlushLog failed");
        }
    }
    else
    {
        ret = archiveLogFile(addName, orgLogFilepath);
        if (ret != OSI_OK)
        {
            logShow("archive log file failed");
        }
    }


    // 统计生成文件耗时
    gettimeofday(&timeAfter, NULL);
    seconds      = timeAfter.tv_sec - timeBefore.tv_sec;
    microseconds = timeAfter.tv_usec - timeBefore.tv_usec;

    if (microseconds < 0)
    {
        seconds--;
        microseconds += 1000000;
    }

    logShow("gen log file spend seconds[%d]  microseconds[%d]", seconds, microseconds);

exit:

    m_memLogSize = 0;
}


#ifdef USE_OSI

/**
 * shell命令设置定时持久化日志
 * @param pThis 注册命令时传入的参数
 * @param recvBuf 从终端获取的shell命令参数
 * @param sendBuf 返回给shell终端的数据
 * @return OSI_OK成功
 */
OSI_INT32 C_LogTextImpl::shellSetFlushInterval(void* pThis, const char* recvBuf, char* sendBuf)
{
    int  sendLen    = 0;
    int  interval   = 0;
    int  ret        = 0;
    char helpInfo[] = "format: logText 6,<interval>  interval range[0,864000]\n"
                      "example1: logText 6,3600  set flush interval to 3600s\n"
                      "example2: logText 6,0     disable flush interval\n";

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    if (C_LogUtils::skipShellSpace(recvBuf) != 0)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        return OSI_OK;
    }

    // OSI_Sscanf_s返回读取到的字段数，如果字符串为空，返回EOF
    ret = OSI_Sscanf_s(recvBuf, "%d", &interval);

    switch (ret)
    {
    case 0:
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        break;
    case 1:
        if (interval < FLUSH_INTERVAL_MIN || interval > FLUSH_INTERVAL_MAX)
        {
            if (SEND_BUF_LEN - sendLen > 0)
            {
                sendLen += snprintf(sendBuf + sendLen,
                                    SEND_BUF_LEN - sendLen,
                                    "illegal param, interval[%d] must in [%d,%d]",
                                    interval,
                                    FLUSH_INTERVAL_MIN,
                                    FLUSH_INTERVAL_MAX);
            }
            break;
        }
        C_LogText::getInstance()->setFlushInterval(interval);
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "set interval[%d] ok\n", interval);
        }
        break;
    default:
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        break;
    }

    return OSI_OK;
}


/**
 * shell命令获取版本
 * @param pThis 注册命令时传入的参数
 * @param recvBuf 从终端获取的shell命令参数
 * @param sendBuf 返回给shell终端的数据
 * @return OSI_OK成功
 */
OSI_INT32 C_LogTextImpl::shellGetVersion(void* pThis, const char* recvBuf, char* sendBuf)
{
    int sendLen = 0;

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    sendLen += snprintf(sendBuf + sendLen,
                        SEND_BUF_LEN - sendLen,
                        "v%s %s\n",
                        LOG_TEXT_VERSION,
                        C_LogUtils::getCompileTimeStr().c_str());
    return OSI_OK;
}

/**
 * shell命令设置打印等级
 * @param pThis 注册命令时传入的参数
 * @param recvBuf 从终端获取的shell命令参数
 * @param sendBuf 返回给shell终端的数据
 * @return OSI_OK成功
 */
OSI_INT32 C_LogTextImpl::shellSetLevel(void* pThis, const char* recvBuf, char* sendBuf)
{
    int  sendLen = 0;
    int  level   = 0;
    char module[200];
    int  ret        = 0;
    char helpInfo[] = "format: logText 2,<level>[,moduleName]\n"
                      "level: 0[trace] 1[info] 2[report] 3[warning] 4[error] 5[fault]\n"
                      "example: logText 2,1       set global level to info\n"
                      "example: logText 2,1,test  set test module level to info\n";

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    memset_s(&module, sizeof(module), 0);

    if (C_LogUtils::skipShellSpace(recvBuf) != 0)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        return OSI_OK;
    }

    // OSI_Sscanf_s返回读取到的字段数，如果字符串为空，返回EOF
    ret = OSI_Sscanf_s(recvBuf, "%d,%s", &level, module);

    switch (ret)
    {
    case 0:
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        break;
    case 1:
        if (level < LOG_LEVEL_TRACE || level > LOG_LEVEL_FAULT)
        {
            if (SEND_BUF_LEN - sendLen > 0)
            {
                sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "logLevel err!!\n%s", helpInfo);
            }
            break;
        }
        C_LogText::getInstance()->setLogLevel((LOG_LEVEL)level);
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(
                sendBuf + sendLen, SEND_BUF_LEN - sendLen, "set logLevel ok, scope[%s] level[%d]\n", "global", level);
        }
        break;
    case 2:
        if (level < LOG_LEVEL_TRACE || level > LOG_LEVEL_FAULT)
        {
            if (SEND_BUF_LEN - sendLen > 0)
            {
                sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "logLevel err!!\n%s", helpInfo);
            }
            break;
        }
        C_LogText::getInstance()->setLogLevel((LOG_LEVEL)level, module);
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(
                sendBuf + sendLen, SEND_BUF_LEN - sendLen, "set logLevel ok, scope[%s] level[%d]\n", module, level);
        }
        break;
    default:
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        break;
    }

    return OSI_OK;
}

/**
 * shell命令设置日志文件记录等级
 * @param pThis 注册命令时传入的参数
 * @param recvBuf 从终端获取的shell命令参数
 * @param sendBuf 返回给shell终端的数据
 * @return OSI_OK成功
 */
OSI_INT32 C_LogTextImpl::shellSetLevelFile(void* pThis, const char* recvBuf, char* sendBuf)
{
    int  sendLen    = 0;
    int  level      = 0;
    int  ret        = 0;
    char helpInfo[] = "log Level: 0[trace] 1[info] 2[report] 3[warning] 4[error] 5[fault]\n"
                      "format: logText 5,<logLevel>\n"
                      "example1: logText 5,1   set info level to record log to file\n"
                      "example2: logText 5,-1  set level equal to output log level\n";

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    if (C_LogUtils::skipShellSpace(recvBuf) != 0)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        return OSI_OK;
    }

    // OSI_Sscanf_s返回读取到的字段数，如果字符串为空，返回EOF
    ret = OSI_Sscanf_s(recvBuf, "%d", &level);

    switch (ret)
    {
    case 0:
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        break;
    case 1:
        C_LogText::getInstance()->setLogFileLevel(level);
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", "set ok");
        }
        break;
    default:
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        break;
    }

    return OSI_OK;
}

/**
 * shell命令将日志刷入到文件
 * @param pThis 注册命令时传入的参数
 * @param recvBuf 从终端获取的shell命令参数
 * @param sendBuf 返回给shell终端的数据
 * @return OSI_OK成功
 */
OSI_INT32 C_LogTextImpl::shellFlush(void* pThis, const char* recvBuf, char* sendBuf)
{
    int            sendLen = 0;
    FLUSH_INFO     info    = {LOG_FLUSH_TYPE_NOT_FLUSH, 0};
    C_LogTextImpl* pObj    = (C_LogTextImpl*)pThis;

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    info.flushType = LOG_FLUSH_TYPE_MANUAL;
    pObj->flushLogWithInfo(info);
    if (SEND_BUF_LEN - sendLen > 0)
    {
        sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "flushLog ok\n");
    }
    return OSI_OK;
}

/**
 * 打开终端输出
 * @param pThis 注册命令时传入的参数
 * @param recvBuf 从终端获取的shell命令参数
 * @param sendBuf 返回给shell终端的数据
 * @return OSI_OK成功
 */
OSI_INT32 C_LogTextImpl::shellOpenStdout(void* pThis, const char* recvBuf, char* sendBuf)
{
    int sendLen = 0;

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    C_LogText::getInstance()->setStdOutput(true);
    if (SEND_BUF_LEN - sendLen > 0)
    {
        sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "outputOpen ok\n");
    }
    return OSI_OK;
}

/**
 * 关闭终端输出
 * @param pThis 注册命令时传入的参数
 * @param recvBuf 从终端获取的shell命令参数
 * @param sendBuf 返回给shell终端的数据
 * @return OSI_OK成功
 */
OSI_INT32 C_LogTextImpl::shellCloseStdout(void* pThis, const char* recvBuf, char* sendBuf)
{
    int sendLen = 0;

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    C_LogText::getInstance()->setStdOutput(false);
    if (SEND_BUF_LEN - sendLen > 0)
    {
        sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "outputClose ok\n");
    }
    return OSI_OK;
}

/**
 * 展示logText模块的相关信息
 * @param pThis 注册命令时传入的参数
 * @param recvBuf 从终端获取的shell命令参数
 * @param sendBuf 返回给shell终端的数据
 * @return OSI_OK成功
 */
OSI_INT32 C_LogTextImpl::shellShowInfo(void* pThis, const char* recvBuf, char* sendBuf)
{
    int  sendLen         = 0;
    char levelName[][10] = {"Trace", "Info", "Report", "Warning", "Error", "Fault"};
    std::map<std::string, LOG_LEVEL>::iterator it;
    int                                        ret         = 0;
    int                                        param       = 0;
    bool                                       showAllInfo = false;

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    ret = OSI_Sscanf_s(recvBuf, "%d", &param);
    if (ret == 1 && param == 1)
    {
        // 默认只展示基本信息 防止信息过多用户看不懂 只有在命令后面跟1才打印出所有信息
        showAllInfo = true;
    }

    SHOW_PROCESS_NAME();

    // 是否只写内存目录
    if (SEND_BUF_LEN - sendLen > 0)
    {
        sendLen +=
            snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "memOnly[%d]\n", C_LogTextImpl::getObjPtr()->m_memOnly);
    }

    // 持久化日志间隔时间
    if (SEND_BUF_LEN - sendLen > 0)
    {
        sendLen += snprintf(sendBuf + sendLen,
                            SEND_BUF_LEN - sendLen,
                            "flushInterval[%d]\n",
                            C_LogTextImpl::getObjPtr()->m_flushInterval);
    }

    if (SEND_BUF_LEN - sendLen > 0)
    {
        // 日志打印等级信息
        sendLen += snprintf(sendBuf + sendLen,
                            SEND_BUF_LEN - sendLen,
                            "print global log level[%d %s]\n",
                            C_LogTextImpl::getObjPtr()->m_globalLogLevel,
                            levelName[C_LogTextImpl::getObjPtr()->m_globalLogLevel]);
    }
    for (it = C_LogTextImpl::getObjPtr()->m_moduleLogLevelMap.begin();
         it != C_LogTextImpl::getObjPtr()->m_moduleLogLevelMap.end();
         it++)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen,
                                SEND_BUF_LEN - sendLen,
                                "pirnt module[%s] level[%d %s]\n",
                                it->first.c_str(),
                                it->second,
                                levelName[it->second]);
        }
    }

    // 日志记录到文件等级信息
    if (SEND_BUF_LEN - sendLen > 0)
    {
        sendLen += snprintf(sendBuf + sendLen,
                            SEND_BUF_LEN - sendLen,
                            "file log level[%d]\n",
                            C_LogTextImpl::getObjPtr()->m_logFileLevel);
    }

    if (SEND_BUF_LEN - sendLen > 0)
    {
        // 日志缓存最大大小
        sendLen += snprintf(sendBuf + sendLen,
                            SEND_BUF_LEN - sendLen,
                            "log mem cache size[%llu] queue size[%llu]\n",
                            C_LogTextImpl::getObjPtr()->m_memBufMaxSize,
                            C_LogTextImpl::getObjPtr()->m_logQueueSize);
    }

    if (SEND_BUF_LEN - sendLen > 0)
    {
        // 日志内存目录
        sendLen += snprintf(sendBuf + sendLen,
                            SEND_BUF_LEN - sendLen,
                            "log mem dir[%s] filename[%s]\n",
                            C_LogTextImpl::getObjPtr()->m_memLogDir.c_str(),
                            TMP_LOG_FILENAME);
    }

    // 有些信息 只有持久化进程需要打印
    if (!C_LogTextImpl::getObjPtr()->m_memOnly || showAllInfo)
    {
        // 打包目录
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen,
                                SEND_BUF_LEN - sendLen,
                                "packDir[%s]\n",
                                C_LogTextImpl::getObjPtr()->m_packDir.c_str());
        }

        if (SEND_BUF_LEN - sendLen > 0)
        {
            // 日志占用持久化目录最大大小
            sendLen += snprintf(sendBuf + sendLen,
                                SEND_BUF_LEN - sendLen,
                                "log disk size[%llu]\n",
                                C_LogTextImpl::getObjPtr()->m_logFileSize);
        }

        // 日志持久化目录
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen,
                                SEND_BUF_LEN - sendLen,
                                "log dir[%s]\n",
                                C_LogTextImpl::getObjPtr()->m_logDir.c_str());
        }
    }

    if (showAllInfo)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            // 临时数据目录
            sendLen += snprintf(sendBuf + sendLen,
                                SEND_BUF_LEN - sendLen,
                                "tmp dir[%s]\n",
                                C_LogTextImpl::getObjPtr()->m_tmpDir.c_str());
        }

        if (SEND_BUF_LEN - sendLen > 0)
        {
            // 当前缓存的日志大小 队列中日志个数 总共已经丢弃日志个数
            sendLen += snprintf(sendBuf + sendLen,
                                SEND_BUF_LEN - sendLen,
                                "log cache size[%llu], log dir size[%llu], log in queue num[%d], drop log num[%llu]\n",
                                C_LogTextImpl::getObjPtr()->m_memLogSize,
                                C_LogUtils::getDirSize(C_LogTextImpl::getObjPtr()->m_packDir),
                                C_LogTextImpl::getObjPtr()->m_queueLogCount,
                                C_LogTextImpl::getObjPtr()->m_dropNum);
        }

        if (SEND_BUF_LEN - sendLen > 0)
        {
            // 是否马上将内存中的日志落盘
            sendLen += snprintf(sendBuf + sendLen,
                                SEND_BUF_LEN - sendLen,
                                "sync to disk soon[%s]\n",
                                C_LogTextImpl::getObjPtr()->m_syncDisk ? "enable" : "disable");
        }

        // 日志备份信息
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen,
                                SEND_BUF_LEN - sendLen,
                                "backupDir[%s] backupFileSize[%llu]\n",
                                C_LogTextImpl::getObjPtr()->m_backupLogDir.c_str(),
                                C_LogTextImpl::getObjPtr()->m_backupLogFileSize);
        }
    }

    return OSI_OK;
}

/**
 * 控制logText是否捕捉信号
 * @param pThis 注册命令时传入的参数
 * @param recvBuf 从终端获取的shell命令参数
 * @param sendBuf 返回给shell终端的数据
 * @return OSI_OK成功
 */
OSI_INT32 C_LogTextImpl::shellCatchSignal(void* pThis, const char* recvBuf, char* sendBuf)
{
    int  sendLen    = 0;
    int  enable     = 0;
    int  ret        = 0;
    char helpInfo[] = "format: logText 9,<0|1>  0:disable 1:enable\n";

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    if (C_LogUtils::skipShellSpace(recvBuf) != 0)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        return OSI_OK;
    }

    // OSI_Sscanf_s返回读取到的字段数，如果字符串为空，返回EOF
    ret = OSI_Sscanf_s(recvBuf, "%d", &enable);

    switch (ret)
    {
    case 0:
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        break;
    case 1:
        if (enable != 0 && enable != 1)
        {
            if (SEND_BUF_LEN - sendLen > 0)
            {
                sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "param err\n%s", helpInfo);
            }
            break;
        }
        C_LogText::getInstance()->setCatchSignal(enable == 1);
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen,
                                SEND_BUF_LEN - sendLen,
                                "set ok, now state[%s]\n",
                                enable == 1 ? "enable" : "disable");
        }
        break;
    default:
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        break;
    }

    return OSI_OK;
}

OSI_INT32 C_LogTextImpl::shellSyncDisk(void* pThis, const char* recvBuf, char* sendBuf)
{
    int  sendLen    = 0;
    int  enable     = 0;
    int  ret        = 0;
    char helpInfo[] = "format: logText 10,<0|1>  0:disable 1:enable\n";

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    if (C_LogUtils::skipShellSpace(recvBuf) != 0)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        return OSI_OK;
    }

    // OSI_Sscanf_s返回读取到的字段数，如果字符串为空，返回EOF
    ret = OSI_Sscanf_s(recvBuf, "%d", &enable);

    switch (ret)
    {
    case 0:
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        break;
    case 1:
        if (enable != 0 && enable != 1)
        {
            if (SEND_BUF_LEN - sendLen > 0)
            {
                sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "param err\n%s", helpInfo);
            }
            break;
        }
        C_LogText::getInstance()->setSyncDisk(enable == 1);
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen,
                                SEND_BUF_LEN - sendLen,
                                "set ok, now state[%s]\n",
                                enable == 1 ? "enable" : "disable");
        }
        break;
    default:
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        break;
    }

    return OSI_OK;
}

OSI_INT32 C_LogTextImpl::shellTftpExportLog(OSI_VOID* pThis, const char* recvBuf, char* sendBuf)
{
    int    sendLen    = 0;
    string scriptPath = C_LogTextImpl::getObjPtr()->m_logDir + EXPORT_LOG_SCRIPT_NAME;
    string cmd        = "chmod +x " + scriptPath + ";" + scriptPath + " " + recvBuf;
    FILE*  pipe       = popen(cmd.c_str(), "r");
    if (!pipe)
    {
        perror("popen failed");
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    // 读取命令的输出,此步骤是必要的，否则命令可能无法正确运行
    char buffer[1024];
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", buffer);
        }
    }

    // 关闭管道
    int status = pclose(pipe);
    if (status == -1)
    {
        perror("pclose failed");
        return OSI_ERROR;
    }

    return OSI_OK;
}

OSI_INT32 C_LogTextImpl::shellTestWrite(OSI_VOID* pThis, const char* recvBuf, char* sendBuf)
{
    int  sendLen     = 0;
    int  writeLogNum = 0;
    int  logLevel    = 0;
    int  ret         = 0;
    char helpInfo[]  = "Format: logText 12,<writeLogNum>,<logLevel>\n"
                       "Parameters:\n"
                       "  writeLogNum: Number of test logs to write (>0)\n"
                       "  logLevel: Log level to use\n"
                       "    0: TRACE\n"
                       "    1: INFO\n"
                       "    2: REPORT\n"
                       "    3: WARNING\n"
                       "    4: ERROR\n"
                       "    5: FAULT\n"
                       "Example:\n"
                       "  logText 12,1000,4  # Write 1000 test logs at ERROR level\n";

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    if (C_LogUtils::skipShellSpace(recvBuf) != 0)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        return OSI_OK;
    }

    // OSI_Sscanf_s返回读取到的字段数，如果字符串为空，返回EOF
    ret = OSI_Sscanf_s(recvBuf, "%d,%d", &writeLogNum, &logLevel);

    if (ret != 2)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        return OSI_OK;
    }

    if (writeLogNum <= 0)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "writeLogNum must bigger than 0\n");
        }
        return OSI_OK;
    }
    if (logLevel < LOG_LEVEL_TRACE || logLevel > LOG_LEVEL_FAULT)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "logLevel err\n");
        }
        return OSI_OK;
    }

    // 开始打印日志 并记录耗时
    struct timeval startTime     = {0};
    struct timeval endTime       = {0};
    char           resInfo[128]  = {0};
    bool           memOnlyBackup = C_LogTextImpl::getObjPtr()->m_memOnly;   // 备份当前的memOnly状态
    bool           dropLogBackup = C_LogTextImpl::getObjPtr()->m_dropLog;   // 备份当前的dropLog状态

    C_LogText::getInstance()->flushLog();            // 先持久化当前内存中的日志 腾出空间
    C_LogTextImpl::getObjPtr()->m_memOnly = true;    // 设置只写内存 防止持久化操作影响性能统计
    C_LogTextImpl::getObjPtr()->m_dropLog = false;   // 设置不丢弃日志 防止丢弃日志导致统计不准确

    // 暂不考虑测试的时候校时
    gettimeofday(&startTime, NULL);
    for (int i = 0; i < writeLogNum; i++)
    {
        C_LogText::getInstance()->logf((LOG_LEVEL)logLevel,
                                       "testWriteLog",
                                       __FILE__,
                                       __FUNCTION__,
                                       __LINE__,
                                       "test write log num[%d] index[%d]",
                                       2,
                                       writeLogNum,
                                       i);
    }
    gettimeofday(&endTime, NULL);

    C_LogTextImpl::getObjPtr()->m_memOnly = memOnlyBackup;   // 恢复memOnly状态
    C_LogTextImpl::getObjPtr()->m_dropLog = dropLogBackup;   // 恢复dropLog状态

    double timeUsed =
        (endTime.tv_sec - startTime.tv_sec) * 1000.0 + (endTime.tv_usec - startTime.tv_usec) / 1000.0;   // 转换为毫秒

    OSI_Snprintf(resInfo,
                 sizeof(resInfo),
                 "write %d logs complete, time used: %.2f ms, average: %.2f ms per log, %.2f logs per second\n",
                 writeLogNum,
                 timeUsed,
                 timeUsed / writeLogNum,
                 writeLogNum * 1000 / timeUsed);

    // printf和log都输出一下结果
    printf("%s\n", resInfo);   // 日志不输出到串口时 通过printf输出结果
    usleep(1000 * 100);        // 等待 确保日志写入完成
    C_LogText::getInstance()->logf(LOG_LEVEL_FAULT,
                                   "testWriteLog",
                                   __FILE__,
                                   __FUNCTION__,
                                   __LINE__,
                                   "%s",
                                   1,
                                   resInfo);   // 日志输出到串口时 通过log输出结果

    return OSI_OK;
}


#endif

/**
 * 信号处理函数
 * @param signum 信号量值
 */
void C_LogTextImpl::signalHandler(int signum)
{
    //     map<int, string> sig2StrMap = {
    //         {SIGINT, "SIGINT kill by ctrl+c"},
    //         {SIGTERM, "SIGTERM kill by kill cmd"},
    //         {SIGSEGV, "SIGSEGV memory err"},
    //         {SIGABRT, "SIGABRT may cause by 1.double free ptr  2.call abort()  3.call assert()"}};
    //     string             errStr      = "unknow";
    //     string             dmesgStr    = "";
    //     int                ret         = 0;
    //     int                maxDmesgLen = ONE_LOG_MAX_LEN - 200;   // 200是给日志前面的时间戳等字段预留的
    //     unsigned long long timeStart   = 0;
    //     unsigned long long timeEnd     = 0;

    //     timeStart = C_LogUtils::getTimeNowUs();

    //     // 查找信号对应的错误信息
    //     if (sig2StrMap.find(signum) != sig2StrMap.end())
    //     {
    //         errStr = sig2StrMap[signum];
    //     }

    //     logfModuleF("SIGNAL", "--------------------catch signum[%d] signalStr[%s]", signum, errStr.c_str());

    // #ifdef SUPPORT_BACKTRACE
    //     std::string                stackStr;
    //     std::map<int, std::string> sigInfoMap = {
    //         {SIGSEGV, "catch SIGSEGV, mem err signal"},
    //         {SIGABRT, "catch SIGABRT, may cause by 1.double free ptr  2.call abort()  3.call assert()"}};

    //     // 如果是内存非法访问导致的崩溃，记录崩溃的堆栈信息
    //     if (sigInfoMap.find(signum) != sigInfoMap.end())
    //     {
    //         ret = C_LogUtils::printfStack(stackStr);
    //         if (ret == 0)
    //         {
    //             logfModuleF("SIGNAL", (sigInfoMap[signum] + "\nbelow is stack:\n%s").c_str(), stackStr.c_str());
    //         }
    //         else
    //         {
    //             logfModuleF("SIGNAL", sigInfoMap[signum].c_str());
    //         }
    //     }
    // #endif

    //     // 某几种异常信号 需要捕捉dmesg信息
    //     if (SIGSEGV == signum || SIGABRT == signum)
    //     {
    //         ret = C_LogUtils::runCmd("dmesg | tail -100", dmesgStr);
    //         if (0 == ret)
    //         {
    //             // 判断dmesgStr长度是否超过了单条日志的最大长度，如果超过了，则只截取后面的部分
    //             if ((int)dmesgStr.size() > maxDmesgLen)
    //             {
    //                 dmesgStr = dmesgStr.substr(dmesgStr.size() - maxDmesgLen);
    //             }
    //             logfModuleF("SIGNAL", "---------------------------------below is dmesg info\n%s", dmesgStr.c_str());
    //         }
    //         else
    //         {
    //             logfModuleF("SIGNAL", "get dmesg err");
    //         }
    //     }

    //     // 执行注册的处理函数
    //     for (int i = 0; i < (int)C_LogTextImpl::getObjPtr()->m_sigHandlerList.size(); i++)
    //     {
    //         C_LogTextImpl::getObjPtr()->m_sigHandlerList[i](signum, errStr);
    //     }

    //     // 收到程序退出的信号 将内存中的日志写入到文件中
    //     C_LogTextImpl::getObjPtr()->flushLogWithSig(signum);

    //     timeEnd = C_LogUtils::getTimeNowUs();

    //     logShow("handle signal coast [%llu]us", timeEnd - timeStart);


    int   ret = 0;
    char  addName[128];   // 生成的日志文件 额外后缀
    char  cmdRes[128];
    char  cmd[512];
    void* buffer[100];
    int   nptrs = 0;   // 实际捕获到的堆栈个数
    char  tmpBuf[512];
    FILE* fd = NULL;

    if (!C_LogTextImpl::getObjPtr()->m_signalSafeCheck.isSignalSafe())
    {
        // 信号不安全 继续执行后面的收尾操作可能会卡死 所以直接生成coredump

        // 将对应信号的处理函数设置为系统默认处理函数
        signal(signum, SIG_DFL);
        // 向当前进程发送信号，触发系统默认处理函数，比如对于SIGSEGV生成coredump文件
        kill(getpid(), signum);
        return;
    }

    memset_s(addName, sizeof(addName), 0);
    memset_s(cmdRes, sizeof(cmdRes), 0);
    memset_s(cmd, sizeof(cmd), 0);
    memset_s(buffer, sizeof(buffer), 0);
    memset_s(tmpBuf, sizeof(tmpBuf), 0);

    // 将dmesg信息写入日志文件
    snprintf(cmd, sizeof(cmd), "dmesg | tail -100 >> %s", C_LogTextImpl::getObjPtr()->m_infoPathChar);
    C_LogUtils::runCmd(cmd, cmdRes, sizeof(cmdRes));

#ifdef SUPPORT_BACKTRACE
    // 将堆栈信息写入到日志文件
    nptrs = backtrace(buffer, sizeof(buffer));
    // writeLen = 0;
    // if(sizeof(tmpBuf) - writeLen > 0)
    // {
    //     writeLen += snprintf(tmpBuf + writeLen, sizeof(tmpBuf) - writeLen, "\n\n\n-------below is stack num[%d]------\n", nptrs);
    // }
    // for(int i = 0; i < nptrs; i++)
    // {
    //     if(sizeof(tmpBuf) - writeLen > 0)
    //     {
    //         writeLen += snprintf(tmpBuf + writeLen, sizeof(tmpBuf) - writeLen, "stack[%d] addr[%p]\n", i, buffer[i]);
    //     }
    // }

    fd = fopen(C_LogTextImpl::getObjPtr()->m_infoPathChar, "ab");
    if (fd != NULL)
    {
        // fwrite(tmpBuf, writeLen, 1, fd);
        backtrace_symbols_fd(buffer, nptrs, fileno(fd));
        fclose(fd);
    }

#endif


    // 调用系统命令 将内存中的日志文件拷贝到flash
    // snprintf(addName, sizeof(addName), "signal%d", signum);
    // ret = C_LogTextImpl::getObjPtr()->scriptFlushLog(addName);
    // if (ret != OSI_OK)
    // {
    //     logShowNoBlock("scriptFlushLog failed");
    // }

    // todo 如果遇到malloc等比较特殊的崩溃，会导致程序卡死，待解决
    FLUSH_INFO flushInfo = {LOG_FLUSH_TYPE_SIGNAL, signum};
    C_LogTextImpl::getObjPtr()->flushLogWithInfo(flushInfo);


    // 将对应信号的处理函数设置为系统默认处理函数
    signal(signum, SIG_DFL);
    // 向当前进程发送信号，触发系统默认处理函数，比如对于SIGSEGV生成coredump文件
    kill(getpid(), signum);
}


/**
 * @brief 往文件写入内容
 *
 * @param mode fopen的模式参数
 * @param filepath 文件路径
 * @param buf 待写入数据
 * @param len 待写入数据长度
 * @return int 0成功/错误码
 */
int C_LogTextImpl::writeFile(const std::string& filepath, const std::string mode, const char* buf, int len, bool sync)
{
    FILE* fd        = NULL;
    int   ret       = 0;
    int   returnRet = -1;

    fd = fopen(filepath.c_str(), mode.c_str());
    if (NULL == fd)
    {
        // 打开失败可能是因为持久化日志，目录还没来得及创建，稍等一下后重试一次
        usleep(50 * 1000);
        fd = fopen(filepath.c_str(), mode.c_str());
        if (NULL == fd)
        {
            logShow("open file[%s] failed, err[%s]\n", filepath.c_str(), strerror(errno));
            goto exit;
        }
    }
    ret = (int)fwrite(buf, len, 1, fd);
    if (ret != 1)
    {
        logShow("write file[%s] err, ret[%d], err[%s]", filepath.c_str(), ret, strerror(errno));
        goto exit;
    }
    if (sync)
    {
        fflush(fd);
        fsync(fileno(fd));   // 确保数据刷到磁盘
    }
    returnRet = OSI_OK;

exit:
    TRY_FREE_MEM(fd, fclose)
    return returnRet;
}

int C_LogTextImpl::readFile(const std::string& filepath, char* buf, int len)
{
    FILE* fd        = NULL;
    int   ret       = 0;
    int   returnRet = -1;

    fd = fopen(filepath.c_str(), "r");
    if (NULL == fd)
    {
        logShow("open file[%s] failed\n", filepath.c_str());
        goto exit;
    }
    ret = (int)fread(buf, len, 1, fd);
    if (ret != 1)
    {
        logShow("read file[%s] err, ret[%d]", filepath.c_str(), ret);
        goto exit;
    }
    returnRet = 0;

exit:
    TRY_FREE_MEM(fd, fclose)
    return returnRet;
}


/**
 * @brief 获取当前时间字符串
 *
 * @return 当前时间字符串
 */
std::string C_LogTextImpl::getTimeStr()
{
    return C_LogUtils::getLocalTime("ms", "%Y-%m-%dT%H-%M-%S");
}


/**
 * @brief 设置记录到日志文件的日志等级
 *
 * @param level 日志等级，当日志等级不在[0,5]这个范围内，日志文件记录内容和屏幕打印同步
 * @return int 0成功/错误码
 */
int C_LogTextImpl::setLogFileLevel(int level)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setLogFileLevel need init first");
        return LOG_TEXT_NOT_INIT;
    }

    m_logFileLevel = level;
    if (m_logFileLevel >= LOG_LEVEL_MAX_BORDER || m_logFileLevel <= LOG_LEVEL_MIN_BORDER)
    {
        m_logFileLevel = -1;   // 如果日志等级是非法值，则统一置为-1，表示日志文件记录等级跟随打印等级
    }
    return 0;
}

/**
 * @brief 将当前位置更新为环形队列位置
 *
 * @param pos 当前位置
 * @return int
 */
int C_LogTextImpl::queueUpdateLoopPos(int pos)
{
    return pos + ONE_LOG_MAX_LEN >= (int)m_logQueueSize ? 0 : pos;
}

/**
 * @brief 更新队尾位置
 *
 * @return true 成功
 * @return false 失败
 */
bool C_LogTextImpl::queueBackNext()
{
    int startPos;
    int endPos;

    // 获取m_logQueueBack在环形队列中的位置
    m_logQueueBack = queueUpdateLoopPos(m_logQueueBack);

    // 获取下一条日志的首尾位置
    startPos = m_logQueueBack;
    endPos   = startPos + ONE_LOG_MAX_LEN;

    // 判断队列是否满

    // 队列满有一种特殊情况
    m_dataMtx.lock();
    if (startPos == m_logQueueFront && m_queueLogCount > 0)
    {
        m_dataMtx.unlock();
        return false;
    }
    m_dataMtx.unlock();

    // m_logQueueFront如果落在区间(startPos,endPos] 说明队列已经满
    return m_logQueueFront <= startPos || m_logQueueFront > endPos;
}

void C_LogTextImpl::writeExportLogScript()
{
    if (m_memOnly)
    {
        // 只需要把日志写入到内存 不需要操作持久化数据
        logShow("memOnly not need write script");
        return;
    }

    string content = R"(
#!/bin/sh

log_dir=logDirVal
mem_log_filepath=memLogFilePathVal

test_filename=testWrite.txt
writable_dir=/
mem_log_filesize=0
min_mem_log_filesize=10000
output_log_pak_name=logText.tar.gz
help_info="this script help you exprot logText log by tftp
example1: $0 ***********
example2: $0 ***********:69"

# 彩色输出
# 第一个参数颜色
# 第二个参数输出内容
show()
{
    color="37m"
    case $1 in
    black)
        color="30m"
        ;;
    r)
        color="31m" # 红色
        ;;
    g)
        color="32m" # 绿色
        ;;
    y)
        color="33m" # 黄色
        ;;
    b)
        color="34m" # 蓝色
        ;;
    m)
        color="35m" # 品红
        ;;
    c)
        color="36m" # 青色
        ;;
    w)
        color="37m" # 白色
        ;;
    esac
    echo -e "\033[1;${color}$2\033[0m" # 1;表示加粗
}

# 判断目录是否可以写入
check_writable() {
    cd $1
    touch $test_filename >/dev/null 2>&1
}

# 递归判断目录是否可以写入
find_writable_directory() {
    local dir="$1"

    check_writable $1

    if [ "$?" -eq 0 ]; then
        writable_dir=$1
        cd $1
        rm $test_filename
        return
    fi

    for item in "$dir"/*; do
        if [ -d "$item" ]; then
            find_writable_directory "$item"
        fi
    done
}

# 判断是否是合法的ip或者ip+端口格式
check_ip()
{
    # 定义IP地址和端口的正则表达式
    ip_pattern="^([0-9]{1,3}\.){3}[0-9]{1,3}$"
    ip_with_port_pattern="^([0-9]{1,3}\.){3}[0-9]{1,3}(:[0-9]+)?$"

    # 使用正则表达式进行匹配
    if [[ $1 =~ $ip_with_port_pattern ]]; then
        # legal
        return 0
    else
        # illegal
        return 1
    fi
}

# 获取文件大小
get_mem_log_filesize() {
    if [ ! -e "$mem_log_filepath" ]; then
        show b "$mem_log_filepath not exit"
        mem_log_filesize=0
    else
        mem_log_filesize=$(ls -l "$mem_log_filepath" | awk '{print $5}')
    fi
}

export_logText_log()
{
    # 将内存中的日志刷入磁盘
    show b "start flush log to flash"
    logTextFlush
    show b "end flush log to flash"

    # 查找可写入的内存目录
    find_writable_directory /
    show b "find writable dir:$writable_dir"

    # 将日志文件打包
    get_mem_log_filesize
    show b "$mem_log_filepath filesize:$mem_log_filesize"
    show b "start gen log package"
    if [ $mem_log_filesize -gt $min_mem_log_filesize ]; then
        # 内存的日志文件较大，说明logTextFlush失败，需要将内存中的日志一并打包
        tar -czvf $writable_dir/$output_log_pak_name $log_dir/log* $mem_log_filepath
    else
        tar -czvf $writable_dir/$output_log_pak_name $log_dir/log*
    fi
    show b "gen log package ok:$writable_dir/$output_log_pak_name"

    # tftp传输日志文件
    show b "start tftp"
    cd $writable_dir
    tftp -pl "$output_log_pak_name" $1 -b 8192
    if [ $? -ne 0 ]; then
        show r "tftp failed"
        rm $writable_dir/$output_log_pak_name
        exit 1
    fi
    show b "tftp ok"

    # 清理临时生成的日志包
    rm $writable_dir/$output_log_pak_name

    show g "tftp export logText log ok, filename:$output_log_pak_name"
}

if [ $# -eq 0 ]; then
    show w "$help_info"
    exit 1
fi

#check_ip $1
if [ $? -ne 0 ]; then
    show r "ip format err"
    show w "$help_info"
    exit 1
fi

export_logText_log $1
    )";

    string searchStr;
    string replaceStr;
    size_t pos;
    int    ret = 0;

    // 查找子串并替换
    searchStr  = "logDirVal";
    replaceStr = m_logDir;
    pos        = content.find(searchStr);
    if (pos == std::string::npos)
    {
        logShow("write logText script failed, replace str[%s] failed", searchStr.c_str());
        return;
    }
    content.replace(pos, searchStr.length(), replaceStr);
    searchStr  = "memLogFilePathVal";
    replaceStr = m_memLogDir + TMP_LOG_FILENAME;
    pos        = content.find(searchStr);
    if (pos == std::string::npos)
    {
        logShow("write logText script failed, replace str[%s] failed", searchStr.c_str());
        return;
    }
    content.replace(pos, searchStr.length(), replaceStr);

    ret = writeFile(m_logDir + EXPORT_LOG_SCRIPT_NAME, "wb", content.c_str(), content.length(), true);
    if (ret != 0)
    {
        logShow("write logText script file err, ret[%d]", ret);
        return;
    }

    logShow("write logText script file ok");
}

/**
 * shell命令统一入口
 * @param pThis 注册命令时传入的参数
 * @param recvBuf 从终端获取的shell命令参数
 * @param sendBuf 返回给shell终端的数据
 * @return OSI_OK成功
 */
OSI_INT32 C_LogTextImpl::shellLogTextCmd(void* pThis, const char* recvBuf, char* sendBuf)
{
    int  sendLen    = 0;
    int  cmdNum     = 0;
    int  ret        = 0;
    char helpInfo[] = "logText command help:\n"
                      "format: logText <cmdNum>[,param1,param2...]\n"
                      "cmdNum list:\n"
                      "1  - Get version info\n"
                      "     format: logText 1\n"
                      "2  - Set log level\n"
                      "     format: logText 2,<level>[,moduleName]\n"
                      "     level: 0[trace] 1[info] 2[report] 3[warning] 4[error] 5[fault]\n"
                      "     example: logText 2,1       set global level to info\n"
                      "     example: logText 2,1,test  set test module level to info\n"
                      "3  - Flush log to disk\n"
                      "     format: logText 3\n"
                      "4  - Show logText info\n"
                      "     format: logText 4[,1]  1 for detail info\n"
                      "5  - Set log file level\n"
                      "     format: logText 5,<level>  -1:follow print level\n"
                      "6  - Set flush interval\n"
                      "     format: logText 6,<interval>  unit:second\n"
                      "7  - Enable stdout output\n"
                      "     format: logText 7\n"
                      "8  - Disable stdout output\n"
                      "     format: logText 8\n"
                      "9  - Set signal catch\n"
                      "     format: logText 9,<0|1>  0:disable 1:enable\n"
                      "10 - Set sync disk\n"
                      "     format: logText 10,<0|1>  0:disable 1:enable\n"
                      "11 - Export log by tftp\n"
                      "     format: logText 11,<ip[:port]>\n"
                      "12 - Test write log\n"
                      "     format: logText 12,<count>,<level>\n";

    if (recvBuf == NULL || sendBuf == NULL)
    {
        logShow("shellServer err, null point, recvBuf[%p] sendBuf[%p]\n", recvBuf, sendBuf);
        return OSI_ERROR;
    }

    SHOW_PROCESS_NAME();

    if (C_LogUtils::skipShellSpace(recvBuf) != 0)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        return OSI_OK;
    }

    ret = OSI_Sscanf_s(recvBuf, "%d", &cmdNum);
    if (ret != 1)
    {
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "%s", helpInfo);
        }
        return OSI_OK;
    }

    // Skip cmdNum
    const char* params = strchr(recvBuf, ',');
    if (params != NULL)
    {
        params++;   // Skip comma
    }
    else
    {
        // 有些子命令是没有参数的 但是会判断params指针是否为空 所以赋值空字符串
        // 在C++中，字符串字面量存储在程序的只读数据段中，它们的生命周期与程序运行时间一样长，所以不会有生命周期的问题。
        params = "";
    }

    switch (cmdNum)
    {
    case 1: return shellGetVersion(pThis, params, sendBuf);
    case 2: return shellSetLevel(pThis, params, sendBuf);
    case 3: return shellFlush(pThis, params, sendBuf);
    case 4: return shellShowInfo(pThis, params, sendBuf);
    case 5: return shellSetLevelFile(pThis, params, sendBuf);
    case 6: return shellSetFlushInterval(pThis, params, sendBuf);
    case 7: return shellOpenStdout(pThis, params, sendBuf);
    case 8: return shellCloseStdout(pThis, params, sendBuf);
    case 9: return shellCatchSignal(pThis, params, sendBuf);
    case 10: return shellSyncDisk(pThis, params, sendBuf);
    case 11: return shellTftpExportLog(pThis, params, sendBuf);
    case 12: return shellTestWrite(pThis, params, sendBuf);
    default:
        if (SEND_BUF_LEN - sendLen > 0)
        {
            sendLen += snprintf(sendBuf + sendLen, SEND_BUF_LEN - sendLen, "Invalid cmdNum[%d]\n%s", cmdNum, helpInfo);
        }
        break;
    }

    return OSI_OK;
}

// 修改注册shell命令的函数
void C_LogTextImpl::regLogTextShellCmd()
{
#ifdef USE_OSI
    // 注册统一的shell命令
    if (regShellCmd("logText", shellLogTextCmd, this) != OSI_OK)
    {
        logShow("regShellCmd logText err\n");
    }
#endif
}

// 检查入参
int C_LogTextImpl::checkInitParam(const LOG_TEXT_INIT_PARAM_V2& initParamV2)
{
    int                 returnRet = LOG_TEXT_OK;
    LOG_TEXT_INIT_PARAM initParam = initParamV2.initParam;

    m_processName = initParam.processName;
    if (m_processName.empty())
    {
        C_LogUtils::getProcessName(m_processName);
    }

    m_logDir = initParam.logDir;
    if (!C_LogUtils::mkFormatDir(m_logDir))
    {
        logShow("make log dir[%s] failed", m_logDir.c_str());
        returnRet = LOG_TEXT_PARAM_ERR;
        goto exit;
    }

    m_memLogDir = initParam.memLogDir;

    if (m_memLogDir.empty())
    {
        m_memLogDir = TMP_LOG_DIR;
    }
    if (!C_LogUtils::mkFormatDir(m_memLogDir))
    {
        logShow("make mem log dir[%s] failed", m_memLogDir.c_str());
        returnRet = LOG_TEXT_PARAM_ERR;
        goto exit;
    }

    m_tmpDir = initParamV2.tmpDir;
    if (m_tmpDir.empty())
    {
        m_tmpDir = TMP_SCRIPT_DIR;
    }
    if (!C_LogUtils::mkFormatDir(m_tmpDir))
    {
        logShow("make tmp dir[%s] failed", m_tmpDir.c_str());
        returnRet = LOG_TEXT_PARAM_ERR;
        goto exit;
    }
    // 检查读写权限
    if (access(m_tmpDir.c_str(), R_OK | W_OK) != 0)
    {
        logShow("dir not have permission to dir[%s]", m_tmpDir.c_str());
        returnRet = LOG_TEXT_PARAM_ERR;
        goto exit;
    }

    snprintf(m_infoPathChar, sizeof(m_infoPathChar), "%s/%s", m_memLogDir.c_str(), DUMP_INFO_FILENAME);

    // 内存日志目录不能和持久化日志目录相同
    if (m_logDir == m_memLogDir)
    {
        logShow("logDir memLogDir must be different");
        returnRet = LOG_TEXT_PARAM_ERR;
        goto exit;
    }

    // 打包目录默认和日志内存目录一致，除非特殊指定
    m_packDir = initParam.packDir;
    if (m_packDir.empty())
    {
        m_packDir = m_memLogDir;
    }

    m_logFileSize   = initParam.logFileSize;
    m_memBufMaxSize = initParam.memBufMaxSize;
    m_logQueueSize  = initParam.logQueueSize;
    m_bCatchSignal  = initParam.catchSignal;
    m_syncDisk      = initParam.syncDisk;
    m_memOnly       = initParam.memOnly;

    // 为未赋值的参数赋默认值
    if (m_logFileSize == 0)
    {
        m_logFileSize = LOG_FILE_SIZE_DEFAULT;
    }
    if (m_memBufMaxSize == 0)
    {
        m_memBufMaxSize = MEM_BUF_SIZE_DEFAULT;
    }
    if (m_logQueueSize == 0)
    {
        m_logQueueSize = LOG_QUEUE_SIZE_DEFAULT;
    }

    // 校验数据
    if (m_logFileSize < LOG_FILE_SIZE_MIN || m_logFileSize > LOG_FILE_SIZE_MAX)
    {
        logShow("init logText err, logFileSize[%llu] is illegal, must in range[%d,%d]\n",
                m_logFileSize,
                LOG_FILE_SIZE_MIN,
                LOG_FILE_SIZE_MAX);
        returnRet = LOG_TEXT_PARAM_ERR;
        goto exit;
    }

    if (m_memBufMaxSize < MEM_BUF_SIZE_MIN || m_memBufMaxSize > MEM_BUF_SIZE_MAX)
    {
        logShow("init logText err, memBufMaxSize[%llu] is illegal, must in range[%d,%d]\n",
                m_memBufMaxSize,
                MEM_BUF_SIZE_MIN,
                MEM_BUF_SIZE_MAX);
        returnRet = LOG_TEXT_PARAM_ERR;
        goto exit;
    }

    if (m_logQueueSize < LOG_QUEUE_SIZE_MIN || m_logQueueSize > LOG_QUEUE_SIZE_MAX)
    {
        logShow("init logText err, logQueueSize[%llu] is illegal, must in range[%d,%d]\n",
                m_logQueueSize,
                LOG_QUEUE_SIZE_MIN,
                LOG_QUEUE_SIZE_MAX);
        returnRet = LOG_TEXT_PARAM_ERR;
        goto exit;
    }

#ifndef USE_ZLIB
    // 如果没有使用压缩 还要判断一下 缓存大小不能大于硬盘中可用大小
    if (m_memBufMaxSize >= m_logFileSize)
    {
        logShow("cache log size[%llu] can not bigger then disk log max size[%llu]",
                initParam.memBufMaxSize,
                initParam.logFileSize);
        returnRet = LOG_TEXT_PARAM_ERR;
        goto exit;
    }
#endif

exit:
    return returnRet;
}

// 归档因为意外断电而未保存的临时文件
int C_LogTextImpl::atchivistTmpLog()
{
    string tmpLogFilepath;
    int    ret = 0;
    string cmd;
    string res;

    if (m_memOnly)
    {
        // 只需要把日志写入到内存 不需要操作持久化数据
        logShow("memOnly not need atchivist tmp log");
        return LOG_TEXT_OK;
    }

    // 如果程序因为意外断电退出 则可能会存在临时的日志文件 将该临时日志文件归档并删除
    cmd = "mv " + m_logDir + TMP_LOG_FILENAME + " " + m_memLogDir + TMP_LOG_FILENAME;
    ret = C_LogUtils::runCmd(cmd, res);
    if (ret != 0)
    {
        logShow("mv to atchivistTmpLog err, ret[%d]", ret);
    }

    return LOG_TEXT_OK;
}

// 处理队列中的日志
void C_LogTextImpl::dealLogInQueue()
{
#define STR_MAX_LEN (1024)

    C_LogTextImpl* logTextImplPtr = C_LogTextImpl::getObjPtr();
    LOG_HEADER     logHeader;
    char*          logReadBuf       = NULL;
    int            readLen          = 0;
    bool           colorOutput      = false;
    char           levelColor[][10] = {F_WHITE, F_WHITE, F_GREEN, F_YELLOW, F_MAGENTA, F_RED, F_RED};
    int            ret              = 0;
    int            logStrLen        = 0;
    int            logCount         = 0;
    int            dealLogCount     = 0;   // 记数本轮已经处理了几条日志

    memset_s(&logHeader, sizeof(logHeader), 0);

    logTextImplPtr->m_dataMtx.lock();
    logCount = logTextImplPtr->m_queueLogCount;
    logTextImplPtr->m_dataMtx.unlock();

    // 一直读 直到队列中没有数据
    while (logCount > 0)
    {
        // 每轮次最多处理100条日志
        // 日志量如果一直非常大 处理不过来 一直在这个循环里处理日志 会导致日志持久化等任务无法被执行到
        if (dealLogCount++ >= MAX_DEAL_LOG_COUNT_PER_TIME)
        {
            break;
        }

        // 从队列中取日志
        logReadBuf = logTextImplPtr->m_pLogQueue + logTextImplPtr->m_logQueueFront;
        readLen    = 0;

        // 读取日志信息
        // 日志信息格式 “日志等级+日志内容”
        memset_s(&logHeader, sizeof(logHeader), 0);
        OSI_Memcpy_Safe(&logHeader, logReadBuf, sizeof(logHeader));
        readLen += sizeof(logHeader);

        // 终端打印日志
        if (logTextImplPtr->m_stdOutput && logHeader.output)
        {
            if (logHeader.logLevel <= LOG_LEVEL_MIN_BORDER || logHeader.logLevel >= LOG_LEVEL_MAX_BORDER)
            {
                logShow("logHeader.logLevel[%d] is err", logHeader.logLevel);
            }
            else
            {
                colorOutput = logTextImplPtr->m_colorOutput;
                if (colorOutput)
                {
                    write(STDOUT_FILENO, CSI_START, OSI_Strnlen_Safe(CSI_START, STR_MAX_LEN));
                    write(STDOUT_FILENO, "1;40;", OSI_Strnlen_Safe("1;40;", STR_MAX_LEN));
                    write(STDOUT_FILENO,
                          levelColor[logHeader.logLevel],
                          OSI_Strnlen_Safe(levelColor[logHeader.logLevel], STR_MAX_LEN));
                    // printf("%s", CSI_START);
                    // printf("%s", "1;40;");   // 1字体加粗 40黑色背景
                    // printf("%s", levelColor[logHeader.logLevel]);
                }
                write(STDOUT_FILENO, logReadBuf + readLen, OSI_Strnlen_Safe(logReadBuf + readLen, ONE_LOG_MAX_LEN));
                // printf("%s", logReadBuf + readLen);
                if (colorOutput)
                {
                    write(STDOUT_FILENO, CSI_END, OSI_Strnlen_Safe(CSI_END, STR_MAX_LEN));
                    // printf("%s", CSI_END);
                }
                fflush(stdout);   // 将打印结果输出到屏幕上，防止颜色字符未输出导致后面输出颜色错乱
            }
        }

        if (logHeader.record)
        {
            // 不再根据日志文件大小触发持久化，而是判断打包目录大小触发持久化
            // // 当缓存大小达到指定值时压缩日志并生成日志文件
            // if (logTextImplPtr->m_memLogSize + ONE_LOG_MAX_LEN >= logTextImplPtr->m_memBufMaxSize)
            // {
            //     logShow("flush log file cause by log size");
            //     logTextImplPtr->compressAndWriteFile();
            // }

            logStrLen = OSI_Strnlen_Safe(logReadBuf + readLen, ONE_LOG_MAX_LEN);
            logTextImplPtr->m_memLogSize += logStrLen;

            // 将日志存放到指定的内存节点中
            if (!logTextImplPtr->m_memLogDir.empty())
            {
                ret = writeFile(logTextImplPtr->m_memLogDir + TMP_LOG_FILENAME, "ab", logReadBuf + readLen, logStrLen);
                if (ret != 0)
                {
                    logShow("sync log to file err, ret[%d]", ret);
                }
            }

            // 如果设置了每条日志马上写入磁盘 则将日志写入到磁盘
            if (logTextImplPtr->m_syncDisk && !logTextImplPtr->m_memOnly)
            {
                ret =
                    writeFile(logTextImplPtr->m_logDir + TMP_LOG_FILENAME, "ab", logReadBuf + readLen, logStrLen, true);
                if (ret != 0)
                {
                    logShow("sync log to file err, ret[%d]", ret);
                }
            }
        }

        // 更新循环队列队首标记 指向下一个可读位置
        logTextImplPtr->m_logQueueFront =
            logTextImplPtr->queueUpdateLoopPos(logTextImplPtr->m_logQueueFront + logHeader.len);
        logTextImplPtr->m_dataMtx.lock();
        logTextImplPtr->m_queueLogCount--;
        logCount = logTextImplPtr->m_queueLogCount;
        logTextImplPtr->m_dataMtx.unlock();
    }
}


// 获取当前日志文件信息
int C_LogTextImpl::getLogFileInfo(std::string logDir, vector<LOG_FILE_INFO>& logFileInfos)
{
    DIR*           dir  = NULL;
    struct dirent* ent  = NULL;
    int            ret1 = 0;
    string         filename;
    string         latestFilename;
    LOG_FILE_INFO  fileInfo;
    int            latestSerial = 0;

    dir = opendir(logDir.c_str());
    if (dir == NULL)
    {
        logShow("opendir dir[%s] err", logDir.c_str());
        ret1 = LOG_TEXT_READ_DIR_ERR;
        goto exit;
    }

    logShow("start find latest file");
    while ((ent = readdir(dir)) != nullptr)
    {
        // 只搜索普通文件
        if (ent->d_type != DT_REG)
        {
            continue;
        }

        // 判断文件名是否是logText的文件
        filename = ent->d_name;

        if (filename.length() < 10)
        {
            continue;
        }

        if (filename.substr(0, 7) != "logText")
        {
            continue;
        }

        if (!(filename[7] >= '0' && filename[7] <= '9' && filename[8] >= '0' && filename[8] <= '9'))
        {
            continue;
        }

        if (filename[9] >= '0' && filename[9] <= '9')
        {
            fileInfo.serial = (filename[7] - '0') * 100 + (filename[8] - '0') * 10 + (filename[9] - '0') * 1;
        }
        else if (filename[9] == '_')
        {
            fileInfo.serial = (filename[7] - '0') * 10 + (filename[8] - '0') * 1;
        }
        else
        {
            continue;
        }

        fileInfo.filename = filename;
        fileInfo.filepath = logDir + "/" + filename;
        fileInfo.filesize = C_LogUtils::getFilesize(fileInfo.filepath);
        fileInfo.bLatest  = filename.find(LATEST_TAG) != string::npos;
        fileInfo.bV3      = filename.find(LATEST_TAG) != string::npos || filename.find(NORMAL_TAG) != string::npos;

        if (fileInfo.bLatest)
        {
            // 如果出现一些异常情况 导致持久化日志失败 可能会有多个latest文件 这里做个判断 认为序号大的那个为真正的latest文件
            if (fileInfo.serial >= latestSerial)
            {
                latestSerial   = fileInfo.serial;
                latestFilename = fileInfo.filename;
                logShow("latest serial[%d]", latestSerial);
            }
        }
        logFileInfos.push_back(fileInfo);
    }
    logShow("end find latest file");

    // 遍历文件列表 最后最多只能有1个latest文件
    for (auto it = logFileInfos.rbegin(); it != logFileInfos.rend(); ++it)
    {
        if (it->bLatest && it->filename != latestFilename)
        {
            logShow("rename unexpect latest file[%s] to normal", it->filename.c_str());
            if (OSI_OK != rmLatestTag(*it))
            {
                logShow("rmLatestTag err, filename[%s]", it->filename.c_str());
                continue;
            }

            // 更新文件信息
            it->bLatest = false;
            size_t pos  = it->filename.find(LATEST_TAG);
            if (pos != string::npos)
            {
                it->filename.replace(pos, string(LATEST_TAG).length(), NORMAL_TAG);
            }
            it->filepath = logDir + "/" + it->filename;
        }
    }

    // 将文件信息按 新->旧 排序
    sort(logFileInfos.begin(), logFileInfos.end(), [latestSerial](const LOG_FILE_INFO& a, const LOG_FILE_INFO& b) {
        // 优先按版本判断 先删除老版本日志
        if (a.bV3 != b.bV3)
        {
            return a.bV3;
        }
        // 接着按是否有latest标志判断 先删除没latest标志的
        if (a.bLatest != b.bLatest)
        {
            return a.bLatest;
        }
        if ((a.serial > latestSerial && b.serial > latestSerial) ||
            (a.serial < latestSerial && b.serial < latestSerial))
        {
            // 在latestSerial的同一侧  序号越大，文件越新
            return a.serial > b.serial;
        }
        return a.serial < b.serial;
    });

#if 0
    logShow("show logText file rank");
    for (int i = 0; i < logFileInfos.size(); i++)
    {
        logShow("filename: %s", logFileInfos[i].filename.c_str());
    }
#endif

exit:
    if (dir != NULL)
    {
        closedir(dir);
        dir = NULL;
    }

    return ret1;
}

// 组装日志文件名
std::string C_LogTextImpl::genLogFilepath(int serial)
{
    char tmp[256];

    memset_s(tmp, sizeof(tmp), 0);
    snprintf(tmp, sizeof(tmp), "%s%s%03d_%s", m_logDir.c_str(), LOG_FILE_PREFIX, serial, LATEST_TAG);

    return tmp;
}

// 删除旧的日志文件 直到符合要求
int C_LogTextImpl::clearLogFile(long                         compressResLen,
                                unsigned long long           logFileSize,
                                const vector<LOG_FILE_INFO>& logFileInfos)
{
    unsigned long long totalSize = compressResLen;
    int                ret       = 0;

    // 计算占用总大小
    // 使用反向迭代器进行遍历
    for (auto it = logFileInfos.rbegin(); it != logFileInfos.rend(); ++it)
    {
        totalSize += it->filesize;
    }

    // 从最旧的日志文件开始删 直到符合条件
    for (auto it = logFileInfos.rbegin(); it != logFileInfos.rend(); ++it)
    {
        if (totalSize <= logFileSize)
        {
            break;
        }
        totalSize -= it->filesize;
        logShow("remove log file[%s]", it->filepath.c_str());
        ret = remove(it->filepath.c_str());
        if (ret != 0)
        {
            perror("remove file err");
            logShow("remove log file[%s] err", it->filepath.c_str());
        }
    }

    return 0;
}

// 删除文件名中的latest标签
int C_LogTextImpl::rmLatestTag(const LOG_FILE_INFO& logFileInfo)
{
    const std::string toSearch    = LATEST_TAG;   // 要查找的子串
    const std::string toReplace   = NORMAL_TAG;   // 替换用的子串
    string            newFilepath = logFileInfo.filepath;
    int               ret         = 0;

    size_t pos = newFilepath.find(toSearch);
    if (pos == std::string::npos)
    {
        logShow("file[%s] can not remove latest tag", newFilepath.c_str());
        return LOG_TEXT_UNKNOW_ERR;
    }

    newFilepath.replace(pos, toSearch.length(), toReplace);

    ret = rename(logFileInfo.filepath.c_str(), newFilepath.c_str());
    if (ret != 0)
    {
        perror("rename err");
        logShow("rename file[%s] to[%s] err, ret[%d]", logFileInfo.filepath.c_str(), newFilepath.c_str(), ret);
        return ret;
    }

    return 0;
}

/**
* 使用脚本完成内存中的日志落盘
*/
int C_LogTextImpl::scriptFlushLog(const char* addName)
{
    // 信号处理函数调用的函数中 不要有malloc 直接用栈内存
    char                     scriptContent[] = R"(
#!/bin/sh

# 将内存目录的日志持久化到磁盘
# 格式：flushLog.sh tmpDir saveDir addName

suffix=".tar.gz"
timestamp=$(date +"%Y-%m-%dT%H-%M-%S")

# 参数检查
if [ $# -lt 2 ]; then
    echo "param num err"
    exit 1
fi

mem_log_dir=$1
disk_log_dir=$2
add_name=${timestamp}_$3
backup_log_dir=$4

replace_latest() {
    directory=$1
    exclude_file=$2

    #find "$directory" -type f ! -name "$exclude_file" -exec sh -c 'mv "$1" "${1/latest/normal}"' _ {} \;
    find "$directory" -type f ! -name "$exclude_file" -exec sh -c 'if [ ! -e "${1/latest/normal}" ]; then mv "$1" "${1/latest/normal}"; fi' _ {} \;

}

# 移除目录末尾的/
mem_log_dir=${mem_log_dir%/}
disk_log_dir=${disk_log_dir%/}

# 创建持久化目录
mkdir -p $disk_log_dir 2>/dev/null

# 找出latest文件 并获取即将生成的文件编号 生成文件路径
latest_file_name=$(ls -1 $disk_log_dir | grep "latest" | tail -n 1)
old_latest_file_name=$latest_file_name
echo "lastest file name:$latest_file_name"
if [ -z $latest_file_name ]; then
    latest_file_name="logText000_latest_"$add_name
else
    number=${latest_file_name:7:3}
    # 需要删除前导0 否则可能被识别为8进制
    number=${number#"${number%%[!0]*}"}
    # 有些shell不识别指定十进制
    # number=$((10#$number))
    number=$(($number))
    number=$(printf "%03d" $(((${number} + 1)%1000)))
    echo "number:$number"
    latest_file_name="logText"$number"_latest_"$add_name
fi
echo "new file name:${latest_file_name}$suffix"

# 落盘
mem_log_dir_name=${mem_log_dir##*/}
mem_log_father_dir=$mem_log_dir/../

mkdir -p $mem_log_father_dir/$latest_file_name
mv $mem_log_dir/* $mem_log_father_dir/$latest_file_name/
# mv $mem_log_dir $mem_log_father_dir/$latest_file_name
# cp -r $mem_log_dir $mem_log_father_dir/$latest_file_name
# rm $mem_log_dir/logTextTmpLog.txt 2>/dev/null
mkdir -p $mem_log_dir 2>/dev/null

echo "tar -czf $disk_log_dir/${latest_file_name}${suffix} -C $mem_log_father_dir $latest_file_name"
tar -czf $disk_log_dir/${latest_file_name}${suffix} -C $mem_log_father_dir $latest_file_name
rm -r $mem_log_father_dir/$latest_file_name

# 备份日志
if [ -n "$backup_log_dir" ]; then
    mkdir -p $backup_log_dir 2>/dev/null
    cp $disk_log_dir/${latest_file_name}${suffix} $backup_log_dir/
fi

# 修改latest文件名为normal
if [ -n "$old_latest_file_name" ]; then
    echo "old_latest_file_name:[$old_latest_file_name]"
    #new_file_name=${old_latest_file_name//latest/normal}
    #mv $disk_log_dir/$old_latest_file_name $disk_log_dir/$new_file_name
    replace_latest "$disk_log_dir" "${latest_file_name}${suffix}"
    if [ -n "$backup_log_dir" ]; then
        #mv $backup_log_dir/$old_latest_file_name $backup_log_dir/$new_file_name
        replace_latest "$backup_log_dir" "${latest_file_name}${suffix}"
    fi
fi

# 删除持久化的临时文件
rm $disk_log_dir/logTextTmpLog.txt 2>/dev/null

echo "ok"

# todo 检查并控制日志文件总占用大小
    )";
    char                     scriptRes[128]  = {0};
    char                     argsStr[256]    = {0};
    int                      ret             = 0;
    std::vector<std::string> subDirs;

    // 记录打包目录的目录结构，在执行完打包操作之后，恢复目录结构
    C_LogUtils::getSubDirs(m_packDir, subDirs);

    if (addName == NULL)
    {
        logShowNoBlock("addName is NULL");
        return OSI_ERROR;
    }

    snprintf(argsStr,
             sizeof(argsStr),
             "%s %s %s %s",
             m_packDir.c_str(),
             m_logDir.c_str(),
             addName,
             m_backupLogDir.c_str());   // 脚本参数为日志内存目录和日志持久化目录

    ret = C_LogUtils::runScript(m_tmpDir.c_str(), scriptContent, argsStr, scriptRes, sizeof(scriptRes));
    if (ret < 0)
    {
        logShowNoBlock("run flush log script failed");
        return OSI_ERROR;
    }
    logShowNoBlock("run flush log script ok");

    // 重新创建目录结构体
    for (auto& dir : subDirs)
    {
        logShow("rebuild dir[%s]", dir.c_str());
        C_LogUtils::mkFormatDir(dir);
    }

    return OSI_OK;
}

/**
 * 使用api接口持久化日志
 */
int C_LogTextImpl::zlibFlushLog(const vector<LOG_FILE_INFO>& logFileInfos, const char* addName)
{
    string        newLogFilePath;
    string        newFilename;
    C_LogCompress logCompress;

    if (addName == NULL)
    {
        logShowNoBlock("addName is NULL");
        return OSI_ERROR;
    }

    // 持久化日志的目录可能被人为删除 所以尝试创建
    if (!C_LogUtils::mkAllDir(m_logDir))
    {
        logShow("mkAllDir failed, dir[%s]", m_logDir.c_str());
        return OSI_ERROR;
    }

    getLogFilename(logFileInfos, addName, newLogFilePath, newFilename);

    // 压缩临时目录中的日志生成日志包
    logCompress.addDir(m_packDir, true);
    // 平衡flash寿命和内存消耗，如果设置过小flash写放大会影响flash寿命，如果设置过大会消耗内存
    logCompress.setChunckSize(512 * OSI_1KB);
    logCompress.setCompressLevel(Z_DEFAULT_COMPRESSION);
    logCompress.setDstFilepath(newLogFilePath);
    logCompress.setDelFile(true);
    logCompress.setReplaceStr(m_packDir, newFilename);   // 解压后的文件都存放在和压缩包同名的目录下
    if (OSI_OK != logCompress.compressFiles())
    {
        logShow("compress files failed, newLogFilePath[%s]", newLogFilePath.c_str());
        return OSI_ERROR;
    }
    logShow("compress files ok, newLogFilePath[%s]", newLogFilePath.c_str());

    // 在备份目录下创建备份
    if (!m_backupLogDir.empty())
    {
        // todo 拷贝一份文件到备份目录
    }

    // 修改原latest文件为normal
    if (logFileInfos.size() > 0 && logFileInfos[0].bLatest)
    {
        if (OSI_OK != rmLatestTag(logFileInfos[0]))
        {
            logShow("rmLatestTag failed, filepath[%s]", logFileInfos[0].filepath.c_str());
            return OSI_ERROR;
        }
    }

    logShowNoBlock("zlibFlushLog ok");
    return OSI_OK;
}

int C_LogTextImpl::setTestBlockWriteLogTask(int blockTime)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setTestBlockWriteLogTask need init first");
        return LOG_TEXT_NOT_INIT;
    }

    m_testBlockWriteLogTask = blockTime;
    return OSI_OK;
}

int C_LogTextImpl::setDropLog(bool drop)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setDropLog need init first");
        return LOG_TEXT_NOT_INIT;
    }

    m_dropLog = drop;
    return OSI_OK;
}

int C_LogTextImpl::setBackupLog(std::string backupLogDir, unsigned long long backupLogFileSize)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setDropLog need init first");
        return LOG_TEXT_NOT_INIT;
    }

    if (!backupLogDir.empty())
    {
        if (!C_LogUtils::mkFormatDir(backupLogDir))
        {
            logShow("make log dir[%s] failed", backupLogDir.c_str());
            return LOG_TEXT_PARAM_ERR;
        }
    }

    if (backupLogFileSize == 0)
    {
        backupLogFileSize = LOG_FILE_SIZE_DEFAULT;
    }

    if (backupLogFileSize < LOG_FILE_SIZE_MIN || backupLogFileSize > LOG_FILE_SIZE_MAX)
    {
        logShow("backupLogFileSize[%llu] is illegal, must in range[%d,%d]\n",
                backupLogFileSize,
                LOG_FILE_SIZE_MIN,
                LOG_FILE_SIZE_MAX);
        return LOG_TEXT_PARAM_ERR;
    }

    m_backupLogDir      = backupLogDir;
    m_backupLogFileSize = backupLogFileSize;

    return LOG_TEXT_OK;
}

/**
 * @brief 检查日志内存目录是否达到指定大小
 *
 */
void C_LogTextImpl::checkMemLogDirSize()
{
    unsigned long long timeNow = 0;
    unsigned long long dirSize = 0;

    timeNow = C_LogUtils::getUptime();

    // 只有和上次检测到达一定时间间隔才再次检测
    if (timeNow < m_lastCheckDirTime + CHECK_LOG_MEM_DIR_TIME_S)
    {
        return;
    }
    m_lastCheckDirTime = timeNow;

    // 达到指定大小则触发持久化
    dirSize = C_LogUtils::getDirSize(m_packDir);
    if (dirSize > m_memBufMaxSize)
    {
        m_flushInfo.flushType = LOG_FLUSH_TYPE_SIZE;
    }

    // logShow("dirSize[%llu] memBuf[%llu]", dirSize, m_memBufMaxSize);
}

/**
 * @brief 检查是否达到持久化时间
 *
 */
void C_LogTextImpl::checkFlushInterval()
{
    unsigned long long timeNow = 0;

    if (m_flushInterval == 0)
    {
        // m_flushInterval为0时不进行定时持久化
        return;
    }

    timeNow = C_LogUtils::getUptime();
    if ((int)timeNow > m_lastFlushTime + m_flushInterval)
    {
        m_flushInfo.flushType = LOG_FLUSH_TYPE_TIME;
    }
}

/**
* 对成员变量赋初始值
*/
void C_LogTextImpl::initClassParam()
{
    m_queueLogCount     = 0;
    m_logFileSize       = 0;
    m_backupLogFileSize = 0;
    m_memBufMaxSize     = 0;
    m_memLogSize        = 0;
    m_globalLogLevel    = DEFAULT_LOG_LEVEL;
    memset_s(&m_logFormat, sizeof(m_logFormat), -1);   // 全部位置1
    m_inited              = false;
    m_colorOutput         = true;
    m_stdOutput           = true;
    m_pLogQueue           = NULL;
    m_logQueueFront       = 0;
    m_logQueueBack        = 0;
    m_flushInfo.flushType = LOG_FLUSH_TYPE_NOT_FLUSH;
    m_flushInfo.signal    = 0;
    m_writeLogThreadFd    = -1;
    m_bCatchSignal        = true;
    m_syncDisk            = false;
    m_logFileLevel        = -1;   // 默认跟随打印输出等级
    m_dropLog             = true;
    m_dropNum             = 0;
    m_logQueueSize        = 0;
    m_memOnly             = false;
    m_lastCheckDirTime    = 0;
    m_flushInterval       = FLUSH_INTERVAL_DEFAULT;
    m_lastFlushTime       = C_LogUtils::getUptime();

    m_testBlockWriteLogTask = 0;

    memset_s(m_infoPathChar, sizeof(m_infoPathChar), 0);
}


/**
 * @brief 将老的日志文件按logText的文件命名格式重新存放
 *
 * @param oldLogFilepath 老日志文件路径
 * @return int
 */
int C_LogTextImpl::archiveOldLogFile(const std::string& oldLogFilepath)
{
    FLUSH_INFO info;

    info.flushType = LOG_FLUSH_TYPE_OLD;

    compressAndWriteFile(info, oldLogFilepath);   // 为所有从老日志转来的日志文件添加old后缀
    return OSI_OK;
}

/**
 * @brief 归档某个日志文件
 *
 * @param addName 生成日志文件后缀
 * @param orgLogFilepath 待归档的日志文件路径
 * @return int
 */
int C_LogTextImpl::archiveLogFile(const std::string& addName, const std::string& orgLogFilepath)
{
    vector<LOG_FILE_INFO> logFileInfos;
    int                   ret    = 0;
    int                   serial = 0;
    string                latestFilepath;
    string                latestFilepathAfter;
    char                  newLogFile[256] = {0};
    string                suffix;
    size_t                pos = 0;

    if (orgLogFilepath.empty())
    {
        logShow("orgLogFilepath is empty");
        return OSI_ERROR;
    }

    // 获取当前logText所有日志文件信息
    logFileInfos.clear();
    ret = getLogFileInfo(m_logDir, logFileInfos);
    if (ret != OSI_OK)
    {
        logShow("getLogFileInfo failed, ret[%d]", ret);
        return OSI_ERROR;
    }

    // 找到logText当前最新的日志文件 并获取下一个日志编号
    if (logFileInfos.size() != 0)
    {
        for (size_t i = 0; i < logFileInfos.size(); i++)
        {
            if (logFileInfos[i].bLatest)
            {
                serial         = NEXT_SERIAL_NUM(logFileInfos[i].serial);
                latestFilepath = logFileInfos[i].filepath;
            }
        }
    }

    // 将老日志文件重命名为logText格式的文件
    suffix = C_LogUtils::getFileSuffix(orgLogFilepath);   // 保留原日志文件的后缀名
    OSI_Snprintf(newLogFile,
                 sizeof(newLogFile),
                 "%s/logText%03d_%s_%s_%s.%s",
                 m_logDir.c_str(),
                 serial,
                 LATEST_TAG,
                 C_LogUtils::getLocalTime("s", "%Y-%m-%dT%H-%M-%S").substr(0, 19).c_str(),
                 addName.c_str(),
                 suffix.c_str());
    // 再次尝试创建logText的持久化目录，防止任务删除导致写入文件失败
    if (!C_LogUtils::mkAllDir(m_logDir))
    {
        OSI_Debug(DLEVEL_ERROR, "mkAllDir failed, dir[%s]", m_logDir.c_str());
        return OSI_ERROR;
    }
    ret = rename(orgLogFilepath.c_str(), newLogFile);
    if (ret != 0)
    {
        logShow("rename old log file[%s] to [%s] failed", orgLogFilepath.c_str(), newLogFile);
        return OSI_ERROR;
    }
    logShow("rename old log file[%s] to [%s] ok", orgLogFilepath.c_str(), newLogFile);

    // 将原最新文件重新命名
    if (latestFilepath.empty())
    {
        // 之前没有日志文件，不用重命名
        return OSI_OK;
    }
    pos = latestFilepath.find(LATEST_TAG);
    if (pos == std::string::npos)
    {
        logShow("latest file[%s] not find tag[%s]", latestFilepath.c_str(), LATEST_TAG);
        return OSI_ERROR;
    }
    latestFilepathAfter = latestFilepath;
    latestFilepathAfter.replace(pos, string(LATEST_TAG).length(), NORMAL_TAG);
    ret = rename(latestFilepath.c_str(), latestFilepathAfter.c_str());
    if (ret != 0)
    {
        logShow("rename latest log file[%s] to [%s] failed", latestFilepath.c_str(), latestFilepathAfter.c_str());
        return OSI_ERROR;
    }
    logShow("rename latest log file[%s] to [%s] ok", latestFilepath.c_str(), latestFilepathAfter.c_str());

    return OSI_OK;
}

/**
 * @brief 设置固定日志持久化时间间隔
 * 单位：秒
 *
 * @param interval 时间间隔 有效范围[0, 10*24*3600],0表示不生效，默认6*3600
 * @return int
 */
int C_LogTextImpl::setFlushInterval(int interval)
{
    GUARD_DATA_LOCK
    if (!m_inited)
    {
        logShow("setFlushInterval need init first");
        return LOG_TEXT_NOT_INIT;
    }

    if (interval < FLUSH_INTERVAL_MIN || interval > FLUSH_INTERVAL_MAX)
    {
        logShow("illegal param, interval[%d] must in [%d,%d]", interval, FLUSH_INTERVAL_MIN, FLUSH_INTERVAL_MAX);
        return LOG_TEXT_PARAM_ERR;
    }

    m_flushInterval = interval;

    return OSI_OK;
}

/**
 * @brief 刷写所有进程的日志
 * 该函数需要需要在init之后调用才能生效
 * @return int
 */
void C_LogTextImpl::flushLogAllProcess()
{
    // 直接调用shell命令 想当前设备的所有进程发送持久化日志的信号
    OSI_System("logText 3");
}

/**
 * @brief 设置日志刷写回调函数 将会把当前日志文件路径列表按从新到旧的顺序传给回调
 * @param cb 回调函数
 * @return int
 */
int C_LogTextImpl::setFlushLogCb(std::function<void(std::vector<std::string>& logList)> cb)
{
    m_flushLogCb = cb;
    return OSI_OK;
}

/**
* @brief 滚动更新日志文件
* 该接口提供给外部使用，帮助其按照logText的日志文件名格式滚动更新日志文件
* @param logDir 日志文件存放目录
* @param logFileSize 日志文件最大大小
* @param addName 日志文件名尾部追加信息
* @return 下一个可写入的日志文件路径
*/
std::string C_LogTextImpl::rollLogFile(const std::string&       logDir,
                                       const unsigned long long logFileSize,
                                       const std::string&       addName)
{
    vector<LOG_FILE_INFO> logFileInfos;
    string                tmpLogDir = logDir;
    string                logFilePath;
    string                logFilename;

    // 检查并创建目录
    if (!C_LogUtils::mkAllDir(tmpLogDir))
    {
        logShow("mkAllDir failed, dir[%s]", logDir.c_str());
        return "";
    }

    if (OSI_OK != getLogFileInfo(tmpLogDir, logFileInfos))
    {
        logShow("get log file info failed, dir[%s]", tmpLogDir.c_str());
        return "";
    }

    if (OSI_OK != clearLogFile(0, logFileSize, logFileInfos))
    {
        logShow("clear log file failed, dir[%s]", tmpLogDir.c_str());
        return "";
    }

    // 修改原latest文件为normal
    if (logFileInfos.size() > 0 && logFileInfos[0].bLatest)
    {
        if (OSI_OK != rmLatestTag(logFileInfos[0]))
        {
            logShow("rmLatestTag failed, filepath[%s]", logFileInfos[0].filepath.c_str());
            return "";
        }
    }

    getLogFilename(logFileInfos, "", logFilePath, logFilename);
    return logDir + "/" + logFilename + addName + ".tar.gz";
}

/**
* @brief 生成新的日志文件名
*
* @param logFileInfos 日志文件信息列表
* @param addName 生成日志文件后缀
* @param newLogFilepath 新的日志文件路径
* @param newFilename 新的日志文件名
*
*/
void C_LogTextImpl::getLogFilename(const std::vector<LOG_FILE_INFO>& logFileInfos,
                                   const char*                       addName,
                                   std::string&                      newLogFilepath,
                                   std::string&                      newFilename)
{
    int  serial                 = 0;
    char tmpName[256]           = {0};
    char tmpNewLogFilePath[256] = {0};

    if (logFileInfos.empty())
    {
        serial = 0;
    }
    else
    {
        serial = logFileInfos[0].bLatest ? NEXT_SERIAL_NUM(logFileInfos[0].serial) : 0;
    }
    OSI_Snprintf(tmpName,
                 sizeof(tmpName),
                 "logText%03d_%s_%s_%s",
                 serial,
                 LATEST_TAG,
                 C_LogUtils::getLocalTime("s", "%Y-%m-%dT%H-%M-%S").substr(0, 19).c_str(),
                 addName);
    OSI_Snprintf(tmpNewLogFilePath, sizeof(tmpNewLogFilePath), "%s/%s.tar.gz", m_logDir.c_str(), tmpName);

    newLogFilepath = tmpNewLogFilePath;
    newFilename    = tmpName;
}